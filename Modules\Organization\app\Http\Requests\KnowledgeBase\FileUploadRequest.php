<?php

namespace Modules\Organization\Http\Requests\KnowledgeBase;

use Modules\Core\Http\Requests\BaseFormRequest;
use Modules\Organization\Traits\OrganizationAuthorization;

class FileUploadRequest extends BaseFormRequest
{
    use OrganizationAuthorization;

    public function rules(): array
    {
        return [
            'files' => 'required|array|min:1|max:10',
            'files.*' => 'required|file|mimes:pdf,doc,docx,txt|max:10240',
            'name' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:1000'
        ];
    }

    public function authorize(): bool
    {
        return $this->hasOrganizationAdminAccess();
    }
}
