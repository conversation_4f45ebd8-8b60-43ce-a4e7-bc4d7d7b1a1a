<?php

namespace Modules\Organization\Http\Requests\Bot;

use Modules\Core\Http\Requests\BaseFormRequest;
use Modules\Organization\Traits\OrganizationAuthorization;

class BotRequest extends BaseFormRequest
{
    use OrganizationAuthorization;

    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'model' => 'required|string|exists:model_ai,key',
            'status' => 'nullable|string|in:active,inactive',
            'visibility' => 'nullable|string|in:public,private',
            'knowledge_sources' => 'nullable|array',
            'knowledge_sources.*' => 'string|exists:knowledge_bases,uuid',
            'knowledge' => 'nullable|array',
            'knowledge.enabled' => 'nullable|boolean',
            'knowledge.auto_query' => 'nullable|boolean',
            'knowledge.top_k' => 'nullable|integer|min:1|max:20',
            'settings' => 'nullable|array'
        ];
    }

    public function authorize(): bool
    {
        return $this->hasOrganizationAdminAccess();
    }
}
