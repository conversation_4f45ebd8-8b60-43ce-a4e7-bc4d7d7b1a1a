<?php

namespace Modules\Organization\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class InvitationNotificationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        $invitation = $this->resource['invitation'];
        $user = $this->resource['user'];
        $organization = $this->resource['organization'];
        $inviter = $this->resource['inviter'];

        return [
            'user_name' => $user->first_name . ' ' . $user->last_name,
            'organization_name' => $organization->name,
            'inviter_name' => $inviter->first_name . ' ' . $inviter->last_name,
            'role' => $invitation->role,
            'accept_url' => url("/invitations/accept?token={$invitation->token}"),
            'decline_url' => url("/invitations/decline?token={$invitation->token}"),
            'user' => $user,
            'organization' => $organization,
            'inviter' => $inviter,
            'invitation' => $invitation,
        ];
    }

    public static function makeArray($data): array
    {
        return (new static($data))->toArray(request());
    }
}
