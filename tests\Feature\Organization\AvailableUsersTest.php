<?php

namespace Tests\Feature\Organization;

use Tests\TestCase;
use Modules\User\Models\User;
use Modules\Organization\Models\Organization;
use Modules\Organization\Models\OrganizationMember;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class AvailableUsersTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    /** @test */
    public function it_can_query_available_users_directly()
    {
        // Create some users - some in organizations, some not
        $availableUser1 = User::factory()->active()->create([
            'first_name' => '<PERSON>',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'phone' => '+1234567890'
        ]);

        $availableUser2 = User::factory()->active()->create([
            'first_name' => 'Jane',
            'last_name' => 'Smith',
            'email' => '<EMAIL>',
            'phone' => '+0987654321'
        ]);

        // Create a user in an organization (should not appear in results)
        $memberUser = User::factory()->active()->create();
        $organization = Organization::factory()->active()->create();
        OrganizationMember::factory()
            ->forOrganizationAndUser($organization, $memberUser)
            ->member()
            ->create();

        // Test the query logic directly
        $availableUsers = User::query()
            ->select('uuid', 'first_name', 'last_name', 'email', 'phone', 'avatar', 'status')
            ->whereDoesntHave('organizations')
            ->where('status', 'active')
            ->get();

        $emails = $availableUsers->pluck('email')->toArray();

        $this->assertContains('<EMAIL>', $emails);
        $this->assertContains('<EMAIL>', $emails);
        $this->assertNotContains($memberUser->email, $emails);
    }

    /** @test */
    public function it_can_search_available_users_by_name()
    {
        $user1 = User::factory()->active()->create([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>'
        ]);

        $user2 = User::factory()->active()->create([
            'first_name' => 'Jane',
            'last_name' => 'Smith',
            'email' => '<EMAIL>'
        ]);

        // Test search by name
        $searchResults = User::query()
            ->select('uuid', 'first_name', 'last_name', 'email', 'phone', 'avatar', 'status')
            ->whereDoesntHave('organizationMemberships')
            ->where('status', 'active')
            ->where(function ($q) {
                $search = 'John';
                $q->where('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", ["%{$search}%"])
                  ->orWhere('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%");
            })
            ->get();

        $this->assertCount(1, $searchResults);
        $this->assertEquals('<EMAIL>', $searchResults->first()->email);
    }

    /** @test */
    public function it_requires_authentication()
    {
        $response = $this->getJson('/api/v1/auth/users/available');

        $response->assertStatus(401);
    }
}
