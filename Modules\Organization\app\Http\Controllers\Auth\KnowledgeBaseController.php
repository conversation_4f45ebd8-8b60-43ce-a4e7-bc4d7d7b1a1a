<?php

namespace Modules\Organization\Http\Controllers\Auth;

use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Modules\ChatBot\Jobs\RAGFileProcessingJob;
use Modules\ChatBot\Models\KnowledgeBase;
use Modules\Core\Traits\ResponseTrait;
use Modules\Organization\Http\Filters\KnowledgeBaseFilter;
use Modules\Organization\Http\Requests\KnowledgeBase\ViewKnowledgeBaseRequest;
use Modules\Organization\Http\Requests\KnowledgeBase\KnowledgeBaseRequest;
use Modules\Organization\Http\Requests\KnowledgeBase\DeleteKnowledgeBaseRequest;
use Modules\Organization\Http\Requests\KnowledgeBase\BulkKnowledgeBaseRequest;
use Modules\Organization\Http\Requests\KnowledgeBase\FileUploadRequest;
use Modules\Organization\Models\Organization;
use Modules\Organization\Traits\OrganizationAuthorization;
use Illuminate\Support\Str;

class KnowledgeBaseController extends Controller
{
    use ResponseTrait, OrganizationAuthorization;

    public function index(ViewKnowledgeBaseRequest $request, string $orgUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        
        $knowledgeBases = $organization->knowledgeBases()
                                      ->with(['owner:id,name'])
                                      ->filter(new KnowledgeBaseFilter($request))
                                      ->paginate($request->input('limit', 10));

        return $this->paginatedResponse($knowledgeBases, 'Knowledge bases retrieved successfully.');
    }

    public function store(KnowledgeBaseRequest $request, string $orgUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        
        $knowledgeBase = DB::transaction(function () use ($request, $organization) {
            $data = $request->validated();
            $data['owner_type'] = Organization::class;
            $data['owner_id'] = $organization->id;
            $data['uuid'] = Str::uuid();
            
            return KnowledgeBase::create($data);
        });

        return $this->successResponse($knowledgeBase, 'Knowledge base created successfully.', 201);
    }

    public function show(ViewKnowledgeBaseRequest $request, string $orgUuid, string $kbUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $knowledgeBase = $this->findKnowledgeBase($organization, $kbUuid);
        
        return $this->successResponse($knowledgeBase, 'Knowledge base retrieved successfully.');
    }

    public function update(KnowledgeBaseRequest $request, string $orgUuid, string $kbUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $knowledgeBase = $this->findKnowledgeBase($organization, $kbUuid);
        
        $knowledgeBase->update($request->validated());
        
        return $this->successResponse($knowledgeBase->fresh(), 'Knowledge base updated successfully.');
    }

    public function delete(DeleteKnowledgeBaseRequest $request, string $orgUuid, string $kbUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $knowledgeBase = $this->findKnowledgeBase($organization, $kbUuid);
        
        $knowledgeBase->delete();
        
        return $this->successResponse(null, 'Knowledge base deleted successfully.');
    }

    public function destroy(DeleteKnowledgeBaseRequest $request, string $orgUuid, string $kbUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $knowledgeBase = $this->findKnowledgeBase($organization, $kbUuid, true);
        
        $knowledgeBase->forceDelete();
        
        return $this->successResponse(null, 'Knowledge base permanently deleted.');
    }

    public function restore(DeleteKnowledgeBaseRequest $request, string $orgUuid, string $kbUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $knowledgeBase = $this->findKnowledgeBase($organization, $kbUuid, true);
        
        $knowledgeBase->restore();
        
        return $this->successResponse($knowledgeBase->fresh(), 'Knowledge base restored successfully.');
    }

    public function dropdown(ViewKnowledgeBaseRequest $request, string $orgUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        
        $knowledgeBases = $organization->knowledgeBases()
                                      ->where('status', 'active')
                                      ->get(['uuid', 'name'])
                                      ->map(fn($kb) => [
                                          'id' => $kb->uuid,
                                          'name' => $kb->name
                                      ]);

        return $this->successResponse($knowledgeBases, 'Knowledge bases dropdown retrieved successfully.');
    }

    public function bulkDelete(BulkKnowledgeBaseRequest $request, string $orgUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $ids = $request->validated('ids');
        
        $deletedCount = $organization->knowledgeBases()->whereIn('uuid', $ids)->delete();
        
        return $this->successResponse(['deleted_count' => $deletedCount], 'Knowledge bases deleted successfully.');
    }

    public function bulkDestroy(BulkKnowledgeBaseRequest $request, string $orgUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $ids = $request->validated('ids');
        
        $deletedCount = $organization->knowledgeBases()->withTrashed()->whereIn('uuid', $ids)->forceDelete();
        
        return $this->successResponse(['deleted_count' => $deletedCount], 'Knowledge bases permanently deleted.');
    }

    public function bulkRestore(BulkKnowledgeBaseRequest $request, string $orgUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $ids = $request->validated('ids');
        
        $restoredCount = $organization->knowledgeBases()->withTrashed()->whereIn('uuid', $ids)->restore();
        
        return $this->successResponse(['restored_count' => $restoredCount], 'Knowledge bases restored successfully.');
    }

    public function createFromText(KnowledgeBaseRequest $request, string $orgUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        
        $knowledgeBase = DB::transaction(function () use ($request, $organization) {
            $data = $request->validated();
            $data['owner_type'] = Organization::class;
            $data['owner_id'] = $organization->id;
            $data['uuid'] = \Str::uuid();
            $data['type'] = 'text';
            
            $kb = KnowledgeBase::create($data);
            
            // Process text content
            RAGFileProcessingJob::dispatch($kb, $data['content']);
            
            return $kb;
        });

        return $this->successResponse($knowledgeBase, 'Knowledge base created from text successfully.', 201);
    }

    public function createFromFile(FileUploadRequest $request, string $orgUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        
        $knowledgeBase = DB::transaction(function () use ($request, $organization) {
            $file = $request->file('file');
            $data = $request->validated();
            
            $data['owner_type'] = Organization::class;
            $data['owner_id'] = $organization->id;
            $data['uuid'] = \Str::uuid();
            $data['type'] = 'file';
            $data['file_path'] = $file->store('knowledge-bases', 'public');
            
            $kb = KnowledgeBase::create($data);
            
            // Process file
            RAGFileProcessingJob::dispatch($kb, $data['file_path']);
            
            return $kb;
        });

        return $this->successResponse($knowledgeBase, 'Knowledge base created from file successfully.', 201);
    }

    public function retrain(DeleteKnowledgeBaseRequest $request, string $orgUuid, string $kbUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $knowledgeBase = $this->findKnowledgeBase($organization, $kbUuid);
        
        $knowledgeBase->update(['status' => 'processing']);
        
        // Retrain knowledge base
        RAGFileProcessingJob::dispatch($knowledgeBase);
        
        return $this->successResponse($knowledgeBase->fresh(), 'Knowledge base retrain started.');
    }

    private function findKnowledgeBase(Organization $organization, string $kbUuid, bool $withTrashed = false): KnowledgeBase
    {
        $query = $organization->knowledgeBases();
        
        if ($withTrashed) {
            $query->withTrashed();
        }
        
        return $query->where('uuid', $kbUuid)->firstOrFail();
    }
}
