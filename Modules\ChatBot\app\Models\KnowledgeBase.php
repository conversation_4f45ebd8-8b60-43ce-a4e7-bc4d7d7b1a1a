<?php

namespace Modules\ChatBot\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;

class KnowledgeBase extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'uuid',
        'owner_id',
        'owner_type',
        'name',
        'type',
        'storage_path',
        'content',
        'status',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'id',
        'owner_id',
        'owner_type',
    ];

    /**
     * Boot the model.
     */
    protected static function boot(): void
    {
        parent::boot();

        // Generate UUID on creation
        static::creating(function ($knowledgeBase) {
            if (empty($knowledgeBase->uuid)) {
                $knowledgeBase->uuid = (string)Str::uuid();
            }
        });
    }

    /**
     * Get the owner of the knowledge base (polymorphic relationship).
     */
    public function owner(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the bots that use this knowledge base.
     */
    public function bots(): BelongsToMany
    {
        return $this->belongsToMany(Bot::class, 'bot_knowledge_bases');
    }

    /**
     * Get the conversations that use this knowledge base.
     */
    public function conversations(): BelongsToMany
    {
        return $this->belongsToMany(Conversation::class, 'conversation_knowledge_bases');
    }

    /**
     * Scope a query to only include ready knowledge bases.
     */
    public function scopeReady(Builder $query): Builder
    {
        return $query->where('status', 'ready');
    }

    /**
     * Scope a query to only include pending knowledge bases.
     */
    public function scopePending(Builder $query): Builder
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope a query to only include processing knowledge bases.
     */
    public function scopeProcessing(Builder $query): Builder
    {
        return $query->where('status', 'processing');
    }

    /**
     * Scope a query to only include error knowledge bases.
     */
    public function scopeError(Builder $query): Builder
    {
        return $query->where('status', 'error');
    }


    /**
     * Scope a query to filter by type.
     */
    public function scopeOfType(Builder $query, string $type): Builder
    {
        return $query->where('type', $type);
    }

    /**
     * Scope a query to filter by owner.
     */
    public function scopeForOwner(Builder $query, int $ownerId, string $ownerType): Builder
    {
        return $query->where('owner_id', $ownerId)
            ->where('owner_type', $ownerType);
    }

    /**
     * Check if the knowledge base is ready for use.
     */
    public function isReady(): bool
    {
        return $this->status === 'ready';
    }

    /**
     * Check if the knowledge base is being processed.
     */
    public function isProcessing(): bool
    {
        return $this->status === 'processing';
    }

    /**
     * Check if the knowledge base has an error.
     */
    public function hasError(): bool
    {
        return $this->status === 'error';
    }

    /**
     * Check if the knowledge base is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Mark the knowledge base as processing.
     */
    public function markAsProcessing(): bool
    {
        return $this->update(['status' => 'processing']);
    }

    /**
     * Mark the knowledge base as ready.
     */
    public function markAsReady(): bool
    {
        return $this->update(['status' => 'ready']);
    }

    /**
     * Mark the knowledge base as error.
     */
    public function markAsError(string $errorMessage = null): bool
    {
        $metadata = $this->metadata ?? [];
        if ($errorMessage) {
            $metadata['error_message'] = $errorMessage;
            $metadata['error_at'] = now()->toISOString();
        }

        return $this->update([
            'status' => 'error',
            'metadata' => $metadata,
        ]);
    }

    /**
     * Update processing result from RAG webhook.
     */
    public function updateProcessingResult(string $status, int $chunkCount = 0, ?string $error = null): bool
    {
        $updateData = [
            'status' => $status,
            'metadata' => array_merge($this->metadata ?? [], [
                'chunk_count' => $chunkCount,
                'processed_at' => now()->toISOString(),
            ])
        ];

        if ($error) {
            $updateData['metadata']['error'] = $error;
            $updateData['metadata']['error_at'] = now()->toISOString();
        }

        return $this->update($updateData);
    }




    /**
     * Create a knowledge base with optional owner information.
     */
    public static function createKnowledgeBase(array $data, $ownerId = null, $ownerType = null): self
    {
        return static::create([
            'owner_id' => $ownerId ?? auth()->id(),
            'owner_type' => $ownerType ?? get_class(auth()->user()),
            'name' => $data['name'],
            'type' => $data['type'],
            'storage_path' => $data['storage_path'],
            'status' => 'pending'
        ]);
    }


    /**
     * Check if knowledge base can be retrained.
     */
    public function canBeRetrained(): bool
    {
        return in_array($this->status, ['error', 'pending']);
    }

    /**
     * Generate text file path for text content.
     */
    public function generateTextFilePath($content): ?string
    {
        if (empty($content)) {
            return $this->storage_path;
        }

        $fileName = $this->uuid . '.txt';
        $path = "knowledge-bases/" . auth()->user()->uuid . "/{$fileName}";

        Storage::put($path, $content);

        $fileSize = Storage::size($path);
        $mimeType = Storage::mimeType($path);

        // Update storage_path
        $this->update([
            'storage_path' => $path,
            'name' => request('name', $fileName),
            'metadata' => [
                'file_size' => $fileSize,
                'mime_type' => $mimeType,
                'original_name' => request('name', $fileName),
                'uploaded_at' => now()->toISOString(),
            ]
        ]);

        return $path;
    }


}
