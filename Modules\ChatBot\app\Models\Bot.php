<?php

namespace Modules\ChatBot\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Modules\ChatBot\Database\Factories\BotFactory;
use Modules\ChatBot\Enums\BotStatus;
use Modules\ChatBot\Enums\BotVisibility;
use Modules\ChatBot\Enums\BotType;
use Modules\ChatBot\Enums\ConversationStatus;
use Modules\ChatBot\Enums\ToolCallingMode;
use Modules\ModelAI\Models\ModelAI;
use Modules\ModelAI\Models\ModelTool;
use Modules\User\Models\User;

class Bot extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'uuid',
        'api_key',
        'name',
        'logo',
        'description',
        'owner_id',
        'owner_type',
        'model_ai_id',
        'system_prompt',
        'greeting_message',
        'starter_messages',
        'closing_message',
        'parameters',
        'tool_calling_mode',
        'status',
        'visibility',
        'bot_type',
        'is_shareable',
        'metadata',
        'theme',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'starter_messages' => 'array',
        'closing_message' => 'array',
        'parameters' => 'array',
        'metadata' => 'array',
        'theme' => 'array',
        'status' => BotStatus::class,
        'visibility' => BotVisibility::class,
        'bot_type' => BotType::class,
        'tool_calling_mode' => ToolCallingMode::class,
        'is_shareable' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'id',
        'api_key',
        'model_ai_id',
        'owner_id',
        'owner_type',
        'system_prompt',
        'parameters',
        'metadata',
        'deleted_at',
    ];

    /**
     * The attributes that should be appended to the model's array form.
     */
    protected $appends = ['logo_url'];

    /**
     * The columns that are sortable for this model.
     */
    protected $sortable = [
        'id',
        'name',
        'description',
        'status',
        'visibility',
        'type',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    /**
     * Get secure AI parameters for this bot.
     * Merges default parameters from ModelService with bot's custom parameters.
     */
    public function getSecureAIParameters(): array
    {
        $modelService = ModelService::query()
            ->where('model_ai_id', $this->model_ai_id)
            ->active()
            ->latest('updated_at')
            ->first();

        if (!$modelService) {
            return [];
        }

        // Start with default parameters from ModelService
        $defaultParams = $modelService->default_parameters ?? [];

        // Merge with bot's custom parameters (bot parameters override defaults)
        $botParams = $this->parameters ?? [];

        // Only return allowed parameters
        $allowedParams = $modelService->allowed_parameters ?? [];

        $mergedParams = array_merge($defaultParams, $botParams);

        // Filter to only include allowed parameters
        return collect($mergedParams)
            ->only($allowedParams)
            ->filter(function ($value) {
                return !is_null($value) && $value !== '' && $value !== [];
            })
            ->all();
    }


    /**
     * Get the owner of the bot (polymorphic relationship).
     */
    public function owner(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the AI model associated with the bot.
     */
    public function aiModel(): BelongsTo
    {
        return $this->belongsTo(ModelAI::class, 'model_ai_id');
    }

    /**
     * Get all conversations for this bot.
     */
    public function conversations(): HasMany
    {
        return $this->hasMany(Conversation::class);
    }

    /**
     * Get active conversations for this bot.
     */
    public function activeConversations(): HasMany
    {
        return $this->hasMany(Conversation::class)->where('status', ConversationStatus::ACTIVE);
    }

    /**
     * Get knowledge bases associated with this bot.
     */
    public function knowledgeBases(): BelongsToMany
    {
        return $this->belongsToMany(KnowledgeBase::class, 'bot_knowledge_bases');
    }

    /**
     * Get ready knowledge bases for this bot.
     */
    public function readyKnowledgeBases(): BelongsToMany
    {
        return $this->knowledgeBases()->where('knowledge_bases.status', 'ready');
    }

    /**
     * Get all shares for this bot (using new table structure).
     */
    public function shares(): HasMany
    {
        return $this->hasMany(BotShare::class);
    }

    /**
     * Get all share links for this bot.
     */
    public function shareLinks(): HasMany
    {
        return $this->hasMany(BotShareLink::class);
    }

    /**
     * Get active share links for this bot.
     */
    public function activeShareLinks(): HasMany
    {
        return $this->hasMany(BotShareLink::class)->active();
    }

    /**
     * Get users who have access to this bot through shares.
     */
    public function sharedUsers(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'bot_shares', 'bot_id', 'user_id')
            ->withTimestamps();
    }

    /**
     * Get users assigned to this private bot.
     */
    public function assignedUsers(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'bot_users', 'bot_id', 'user_id')
            ->withPivot(['assigned_by', 'assigned_at'])
            ->withTimestamps();
    }

    /**
     * Get bot user assignments.
     */
    public function botUsers(): HasMany
    {
        return $this->hasMany(\Modules\Organization\Models\BotUser::class);
    }

    /**
     * Legacy method for backward compatibility.
     */
    public function userShares(): HasMany
    {
        return $this->shares();
    }

    /**
     * Get available tools for this bot through AI model.
     */
    public function availableTools()
    {
        if (!$this->aiModel) {
            return collect();
        }

        return $this->aiModel->tools()
            ->wherePivot('is_enabled', true)
            ->orderByPivot('priority', 'desc');
    }

    /**
     * Scope a query to only include active bots.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', BotStatus::Active);
    }

    /**
     * Scope a query to only include publicly visible bots.
     */
    public function scopePublic(Builder $query): Builder
    {
        return $query->whereIn('status', BotStatus::publicStatuses());
    }

    /**
     * Scope a query to filter by status.
     */
    public function scopeStatus(Builder $query, BotStatus|string $status): Builder
    {
        if ($status instanceof BotStatus) {
            $status = $status->value;
        }

        return $query->where('status', $status);
    }

    /**
     * Scope a query to filter by owner.
     */
    public function scopeOwnedBy(Builder $query, string $ownerType, int $ownerId): Builder
    {
        return $query->where('owner_type', $ownerType)
            ->where('owner_id', $ownerId);
    }

    /**
     * Scope a query to only include public bots.
     */
    public function scopePublicVisibility(Builder $query): Builder
    {
        return $query->where('visibility', BotVisibility::PUBLIC);
    }

    /**
     * Scope a query to only include private bots.
     */
    public function scopePrivateVisibility(Builder $query): Builder
    {
        return $query->where('visibility', BotVisibility::PRIVATE);
    }

    /**
     * Scope a query to only include personal bots.
     */
    public function scopePersonal(Builder $query): Builder
    {
        return $query->where('bot_type', BotType::PERSONAL);
    }

    /**
     * Scope a query to only include organization bots.
     */
    public function scopeOrganization(Builder $query): Builder
    {
        return $query->where('bot_type', BotType::ORGANIZATION);
    }

    /**
     * Scope a query to only include shareable bots.
     */
    public function scopeShareable(Builder $query): Builder
    {
        return $query->where('is_shareable', true);
    }

    /**
     * Scope a query to include bots accessible by a user.
     */
    public function scopeAccessibleBy(Builder $query, int $userId): Builder
    {
        return $query->where(function ($q) use ($userId) {
            // Own bots
            $q->where(function ($subQ) use ($userId) {
                $subQ->where('owner_id', $userId)
                    ->where('owner_type', get_class(auth()->user()));
            })
                // Public bots
                ->orWhere('visibility', BotVisibility::PUBLIC)
                // Shared bots
                ->orWhereHas('shares', function ($shareQ) use ($userId) {
                    $shareQ->where('user_id', $userId)
                        ->where('status', 'active');
                })
                // Bots from organizations where user is a member
                ->orWhere(function ($orgQ) use ($userId) {
                    $orgQ->where('owner_type', 'Modules\\Organization\\Models\\Organization')
                        ->whereHas('owner', function ($memberQ) use ($userId) {
                            $memberQ->whereHas('members', function ($memberQuery) use ($userId) {
                                $memberQuery->where('user_id', $userId);
                            });
                        });
                });
        });
    }

    /**
     * Get the logo URL for this bot.
     */
    public function getLogoUrlAttribute(): string
    {
        if (!$this->logo) {
            // Return default bot logo URL with full domain
            return url('storage/bot-avatars/bot.png');
        }

        // If logo is already a full URL, return as is
        if (filter_var($this->logo, FILTER_VALIDATE_URL)) {
            return $this->logo;
        }

        // If logo is a storage path, generate full URL with domain
        return url(Storage::url($this->logo));
    }

    /**
     * Get the model name attribute (safe for public display).
     */
    public function getModelNameAttribute(): ?string
    {
        return $this->aiModel?->name;
    }

    /**
     * Get the owner name attribute (safe for public display).
     */
    public function getOwnerNameAttribute(): ?string
    {
        return $this->owner?->full_name ?? $this->owner?->name ?? 'Unknown';
    }


    /**
     * Check if the bot is owned by the current authenticated user.
     */
    public function isOwnedByCurrentUser(): bool
    {
        $currentUser = auth()->user();

        if (!$currentUser) {
            return false;
        }

        return $this->owner_id == $currentUser->id &&
            $this->owner_type == get_class($currentUser);
    }


    /**
     * Check if the bot is active.
     */
    public function isActive(): bool
    {
        return $this->status === BotStatus::Active;
    }

    /**
     * Check if the bot is editable.
     */
    public function isEditable(): bool
    {
        return $this->status->isEditable();
    }

    /**
     * Get the bot's display name.
     */
    public function getDisplayName(): string
    {
        return $this->name;
    }

    /**
     * Get the bot's status label.
     */
    public function getStatusLabel(): string
    {
        return $this->status->label();
    }

    /**
     * Get the bot's status color.
     */
    public function getStatusColor(): string
    {
        return $this->status->color();
    }

    /**
     * Get the tool calling mode label.
     */
    public function getToolCallingModeLabel(): string
    {
        return $this->tool_calling_mode->label();
    }

    /**
     * Get the tool calling mode description.
     */
    public function getToolCallingModeDescription(): string
    {
        return $this->tool_calling_mode->description();
    }

    /**
     * Get a specific parameter value.
     */
    public function getParameter(string $key, mixed $default = null): mixed
    {
        return data_get($this->parameters, $key, $default);
    }

    /**
     * Set a specific parameter value.
     */
    public function setParameter(string $key, mixed $value): void
    {
        $parameters = $this->parameters ?? [];
        data_set($parameters, $key, $value);
        $this->parameters = $parameters;
    }

    /**
     * Get a specific metadata value.
     */
    public function getMetadata(string $key, mixed $default = null): mixed
    {
        return data_get($this->metadata, $key, $default);
    }

    /**
     * Set a specific metadata value.
     */
    public function setMetadata(string $key, mixed $value): void
    {
        $metadata = $this->metadata ?? [];
        data_set($metadata, $key, $value);
        $this->metadata = $metadata;
    }

    /**
     * Check if the bot is public.
     */
    public function isPublic(): bool
    {
        return $this->visibility === BotVisibility::PUBLIC;
    }

    /**
     * Check if the bot is private.
     */
    public function isPrivate(): bool
    {
        return $this->visibility === BotVisibility::PRIVATE;
    }

    /**
     * Check if the bot is personal type.
     */
    public function isPersonal(): bool
    {
        return $this->bot_type === BotType::PERSONAL;
    }

    /**
     * Check if the bot is organization type.
     */
    public function isOrganization(): bool
    {
        return $this->bot_type === BotType::ORGANIZATION;
    }

    /**
     * Check if the bot can be shared.
     * Personal bots use share system, Organization bots use member system.
     */
    public function canBeShared(): bool
    {
        // Organization bots don't use share system - they use organization members/guests
        if ($this->isOrganization()) {
            return false;
        }

        // Personal bots can be shared if enabled
        return $this->is_shareable &&
            $this->isActive() &&
            $this->isPersonal();
    }

    /**
     * Check if a user can share this bot.
     * Only applies to personal bots.
     */
    public function canBeSharedBy(int $userId): bool
    {
        // Organization bots don't use share system
        if ($this->isOrganization()) {
            return false;
        }

        if (!$this->canBeShared()) {
            return false;
        }

        // Personal bots: only owner can share
        return $this->owner_id === $userId;
    }

    /**
     * Check if a user can access this bot.
     */
    public function canBeAccessedBy(int $userId): bool
    {
        // Owner can always access
        if ($this->owner_id === $userId) {
            return true;
        }

        // Public bots can be accessed by anyone
        if ($this->isPublic()) {
            return true;
        }

        // Organization bots: check if user is organization member or guest
        if ($this->isOrganization() && $this->owner_type === 'Modules\\Organization\\Models\\Organization') {
            return $this->owner->hasMember(\Modules\User\Models\User::find($userId));
        }

        // Personal bots: check if user has share
        if ($this->isPersonal()) {
            return $this->shares()
                ->where('user_id', $userId)
                ->exists();
        }

        return false;
    }

    /**
     * Share the bot with a user for a specific shareable entity.
     */
    public function shareWith(int $userId, int $shareableId, string $shareableType): ?BotShare
    {
        if (!$this->canBeShared()) {
            return null;
        }

        // Create or get existing share
        return BotShare::createOrGet($this->id, $userId, $shareableId, $shareableType);
    }

    /**
     * Legacy method: Share the bot with a user (using User as shareable entity).
     */
    public function shareWithUser(int $userId): ?BotShare
    {
        return $this->shareWith($userId, $userId, User::class);
    }

    /**
     * Unshare the bot from a user for a specific shareable entity.
     */
    public function unshareFrom(int $userId, int $shareableId, string $shareableType): bool
    {
        return $this->shares()
                ->where('user_id', $userId)
                ->where('shareable_id', $shareableId)
                ->where('shareable_type', $shareableType)
                ->delete() > 0;
    }

    /**
     * Legacy method: Unshare the bot from a user (using User as shareable entity).
     */
    public function unshareFromUser(int $userId): bool
    {
        return $this->unshareFrom($userId, $userId, User::class);
    }

    /**
     * Get user's permission level for this bot.
     */
    public function getUserPermission(int $userId): ?string
    {
        // Owner has admin permission
        if ($this->owner_id === $userId) {
            return 'admin';
        }

        // Organization bots: check organization member role
        if ($this->isOrganization() && $this->owner_type === 'Modules\\Organization\\Models\\Organization') {
            $user = \Modules\User\Models\User::find($userId);
            if (!$user || !$this->owner->hasMember($user)) {
                return null;
            }

            $memberRole = $this->owner->getMemberRole($user);

            return match ($memberRole) {
                'admin' => 'admin',
                'editor' => 'write',
                'viewer', 'member', 'guest' => 'read',
                default => null,
            };
        }

        // Personal bots: check if user has share
        if ($this->isPersonal()) {
            $hasShare = $this->shares()
                ->where('user_id', $userId)
                ->exists();

            return $hasShare ? 'read' : null;
        }

        return null;
    }

    /**
     * Get organization members who can access this bot.
     * Only applies to organization bots.
     */
    public function getOrganizationMembers()
    {
        if (!$this->isOrganization() || $this->owner_type !== 'Modules\\Organization\\Models\\Organization') {
            return collect();
        }

        try {
            return \Modules\Organization\Models\OrganizationMember::where('organization_id', $this->owner->id)
                ->with('user')->get();
        } catch (Exception $e) {
            return collect();
        }
    }

    /**
     * Get organization member count who can access this bot.
     */
    public function getOrganizationMemberCount(): int
    {
        if (!$this->isOrganization() || $this->owner_type !== 'Modules\\Organization\\Models\\Organization') {
            return 0;
        }

        try {
            return \Modules\Organization\Models\OrganizationMember::where('organization_id', $this->owner->id)->count();
        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * Check if user can perform action on this bot.
     */
    public function userCan(int $userId, string $action): bool
    {
        $permission = $this->getUserPermission($userId);

        if (!$permission) {
            return false;
        }

        return match ($action) {
            'read', 'view', 'use' => in_array($permission, ['read', 'write', 'admin']),
            'write', 'edit' => in_array($permission, ['write', 'admin']),
            'admin', 'delete', 'destroy', 'share' => $permission === 'admin',
            default => false,
        };
    }

    /**
     * Get total number of shares for this bot.
     */
    public function getTotalSharesCount(): int
    {
        return $this->shares()->count();
    }

    /**
     * Get list of users this bot is shared with.
     */
    public function getSharedWithUsers(): Collection
    {
        return $this->sharedUsers()->get();
    }

    /**
     * Check if bot is shared with a specific user.
     */
    public function isSharedWith(int $userId): bool
    {
        return $this->shares()
            ->where('user_id', $userId)
            ->exists();
    }

    /**
     * Create a share link for this bot.
     */
    public function createShareLink(int $shareableId, string $shareableType, ?\DateTimeInterface $expiresAt = null): ?BotShareLink
    {
        if (!$this->canBeShared()) {
            return null;
        }

        return \Modules\ChatBot\Models\BotShareLink::createForBot($this->id, $shareableId, $shareableType, $expiresAt);
    }

    /**
     * Get active share links count.
     */
    public function getActiveShareLinksCount(): int
    {
        return $this->activeShareLinks()->count();
    }

    /**
     * Attach a knowledge base to this bot.
     */
    public function attachKnowledgeBase(int $knowledgeBaseId): void
    {
        $this->knowledgeBases()->syncWithoutDetaching([$knowledgeBaseId]);
    }

    /**
     * Detach a knowledge base from this bot.
     */
    public function detachKnowledgeBase(int $knowledgeBaseId): void
    {
        $this->knowledgeBases()->detach($knowledgeBaseId);
    }

    /**
     * Check if bot has a specific knowledge base.
     */
    public function hasKnowledgeBase(int $knowledgeBaseId): bool
    {
        return $this->knowledgeBases()->where('knowledge_base_id', $knowledgeBaseId)->exists();
    }

    /**
     * Get knowledge base statistics for this bot.
     */
    public function getKnowledgeBaseStats(): array
    {
        return [
            'total_knowledge_bases' => $this->knowledgeBases()->count(),
            'ready_knowledge_bases' => $this->readyKnowledgeBases()->count(),
            'pending_knowledge_bases' => $this->knowledgeBases()->where('status', 'pending')->count(),
            'processing_knowledge_bases' => $this->knowledgeBases()->where('status', 'processing')->count(),
            'error_knowledge_bases' => $this->knowledgeBases()->where('status', 'error')->count(),
        ];
    }


    /**
     * Check if bot has a greeting message.
     */
    public function hasGreetingMessage(): bool
    {
        return !empty($this->greeting_message);
    }


    /**
     * Get the greeting message or default.
     */
    public function getGreetingMessage(): string
    {
        return $this->greeting_message ?? "Xin chào! Tôi là {$this->name}. Tôi có thể giúp gì cho bạn?";
    }

    /**
     * Regenerate API key for the bot.
     */
    public function regenerateApiKey(): string
    {
        $this->api_key = 'pk_' . Str::random(48);
        $this->save();
        return $this->api_key;
    }

    /**
     * Generate a default avatar for the bot.
     */
    public function generateDefaultAvatar(): void
    {
        try {
            // Get first letter of bot name
            $initial = strtoupper(substr(trim($this->name), 0, 1));
            if (empty($initial) || !ctype_alpha($initial)) {
                $initial = 'B'; // Default to 'B' for Bot
            }

            // Generate colors based on bot ID for consistency
            $colors = [
                '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
                '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
                '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2'
            ];
            $bgColor = $colors[$this->id % count($colors)];

            // Create SVG content
            $svg = '<?xml version="1.0" encoding="UTF-8"?>
            <svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                <circle cx="50" cy="50" r="50" fill="' . $bgColor . '"/>
                <text x="50" y="50" font-family="Arial, sans-serif" font-size="40" font-weight="bold"
                      text-anchor="middle" dominant-baseline="central" fill="#FFFFFF">' . $initial . '</text>
            </svg>';

            // Create filename
            $filename = 'logos/bots/' . $this->uuid . '-avatar.svg';

            // Save SVG to storage
            Storage::disk('public')->put($filename, $svg);

            // Update bot with logo path (use direct DB query to avoid triggering events)
            \DB::table('bots')
                ->where('id', $this->id)
                ->update(['logo' => $filename, 'updated_at' => now()]);

            // Update the current model instance
            $this->logo = $filename;

        } catch (\Exception $e) {
            // Log error but don't throw to avoid breaking bot creation
            \Log::warning("Failed to generate avatar for bot {$this->id}: " . $e->getMessage());
        }
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): BotFactory
    {
        return BotFactory::new();
    }
}
