<?php

namespace Modules\Organization\Http\Requests\Bot;

use Modules\Core\Http\Requests\BaseFormRequest;
use Modules\Organization\Traits\OrganizationAuthorization;

class LogoUploadRequest extends BaseFormRequest
{
    use OrganizationAuthorization;

    public function rules(): array
    {
        return [
            'file' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:10240'
        ];
    }

    public function authorize(): bool
    {
        return $this->hasOrganizationAdminAccess();
    }
}
