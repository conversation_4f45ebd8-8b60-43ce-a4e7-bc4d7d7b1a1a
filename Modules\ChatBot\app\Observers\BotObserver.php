<?php

namespace Modules\ChatBot\Observers;

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Modules\ChatBot\Models\Bot;
use Modules\ModelAI\Models\ModelService;

class BotObserver
{
    /**
     * Handle the Bot "creating" event.
     */
    public function creating(Bot $bot): void
    {
        // Generate UUID if not set
        if (empty($bot->uuid)) {
            $bot->uuid = (string)Str::uuid();
        }

        // Generate API key if not set
        if (empty($bot->api_key)) {
            $bot->api_key = 'pk_' . Str::random(48);
        }
    }

    /**
     * Handle the Bot "created" event.
     */
    public function created(Bot $bot): void
    {
        // Generate default avatar if logo is not set
        if (empty($bot->logo)) {
            $bot->generateDefaultAvatar();
        } else {
            // Move logo from temp directory to bot directory
            if (Str::contains($bot->logo, '/temp/')) {
                $this->moveTempLogo($bot);
            }
        }
    }

    /**
     * Handle the Bot "saving" event.
     */
    public function saving(Bot $bot): void
    {
        // Allow parameters before saving
        if ($bot->model_ai_id) {
            $this->allowParameters($bot);
        }
    }

    /**
     * Move logo from temp directory to bot directory.
     */
    private function moveTempLogo(Bot $bot): void
    {
        $filename = basename($bot->logo);
        $newPath = "logos/bots/{$bot->uuid}/{$filename}";

        // Move file from temp to bot directory
        if (Storage::disk('public')->move($bot->logo, $newPath)) {
            $bot->logo = $newPath;
            $bot->save();
        }
    }

    /**
     * Allow parameters based on model AI configuration.
     */
    private function allowParameters(Bot $bot): void
    {
        $modelService = ModelService::query()
            ->where('model_ai_id', $bot->model_ai_id)
            ->active()
            ->latest('updated_at')
            ->first();

        $allowed = $modelService?->allowed_parameters ?? [];

        if (empty($allowed)) {
            $bot->parameters = [];
            return;
        }

        $bot->parameters = collect($bot->parameters ?? [])
            ->only($allowed)
            ->filter(function ($value) {
                return !is_null($value) && $value !== '' && $value !== [];
            })
            ->all();
    }
}
