<?php

namespace Modules\Organization\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrganizationOverviewResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'success' => true,
            'message' => 'Organization overview retrieved successfully',
            'data' => [
                'bots' => [
                    'total' => $this->resource['bots']['total'],
                    'active' => $this->resource['bots']['active'],
                    'draft' => $this->resource['bots']['draft'],
                    'most_used_bot' => $this->resource['bots']['most_used_bot'],
                ],
                'conversations' => [
                    'total' => $this->resource['conversations']['total'],
                    'today' => $this->resource['conversations']['today'],
                    'this_week' => $this->resource['conversations']['this_week'],
                    'this_month' => $this->resource['conversations']['this_month'],
                    'avg_conversation_length' => $this->resource['conversations']['avg_conversation_length'],
                ],
                'members' => [
                    'total' => $this->resource['members']['total'],
                    'active' => $this->resource['members']['active'],
                    'pending' => $this->resource['members']['pending'],
                    'admins' => $this->resource['members']['admins'],
                ],
                'storage' => [
                    'total_used_mb' => $this->resource['storage']['total_used_mb'],
                    'documents_size_mb' => $this->resource['storage']['documents_size_mb'],
                    'attachments_size_mb' => $this->resource['storage']['attachments_size_mb'],
                    'remaining_quota_mb' => $this->resource['storage']['remaining_quota_mb'],
                    'quota_limit_mb' => $this->resource['storage']['quota_limit_mb'],
                    'usage_percent' => $this->resource['storage']['usage_percent'],
                ],
                'tokens' => [
                    'total_used' => $this->resource['tokens']['total_used'],
                    'this_month' => $this->resource['tokens']['this_month'],
                    'estimated_cost' => $this->resource['tokens']['estimated_cost'],
                ],
                'activity' => [
                    'last_active_at' => $this->resource['activity']['last_active_at'],
                    'peak_hours' => $this->resource['activity']['peak_hours'],
                    'popular_bots' => $this->resource['activity']['popular_bots'],
                ],
            ],
        ];
    }
}
