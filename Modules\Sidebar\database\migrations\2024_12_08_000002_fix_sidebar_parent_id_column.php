<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('sidebars', function (Blueprint $table) {
            // First, change parent_id to nullable
            $table->unsignedBigInteger('parent_id')->nullable()->change();
        });

        // Then, update all parent_id = 0 to null
        DB::table('sidebars')->where('parent_id', 0)->update(['parent_id' => null]);

        Schema::table('sidebars', function (Blueprint $table) {
            // Add foreign key constraint
            $table->foreign('parent_id')->references('id')->on('sidebars')->onDelete('cascade');
        });
    }

    public function down(): void
    {
        Schema::table('sidebars', function (Blueprint $table) {
            // Drop foreign key constraint
            $table->dropForeign(['parent_id']);
            
            // Change back to non-nullable with default 0
            $table->bigInteger('parent_id')->default(0)->change();
        });
        
        // Update null values back to 0
        DB::table('sidebars')->whereNull('parent_id')->update(['parent_id' => 0]);
    }
};
