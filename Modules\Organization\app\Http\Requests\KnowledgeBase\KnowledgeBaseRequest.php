<?php

namespace Modules\Organization\Http\Requests\KnowledgeBase;

use Modules\Core\Http\Requests\BaseFormRequest;
use Modules\Organization\Traits\OrganizationAuthorization;

class KnowledgeBaseRequest extends BaseFormRequest
{
    use OrganizationAuthorization;

    public function rules(): array
    {
        $rules = [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'status' => 'nullable|string|in:active,inactive,processing,failed,ready,pending',
            'type' => 'required|string|in:text,file,url',
        ];

        // Rules based on type
        if ($this->input('type') === 'text') {
            $rules['content'] = 'required|string';
        } elseif ($this->input('type') === 'file') {
            $rules['storage_path'] = 'required|string';
        } elseif ($this->input('type') === 'url') {
            $rules['url'] = 'required|string';
        }

        return $rules;
    }

    public function authorize(): bool
    {
        return $this->hasOrganizationAdminAccess();
    }
}
