<?php

namespace Modules\Organization\Services;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Modules\Organization\Models\Organization;
use Modules\ChatBot\Models\Bot;
use Modules\ChatBot\Models\Conversation;
use Modules\ChatBot\Models\Message;
use Modules\ChatBot\Models\KnowledgeBase;
use Modules\Organization\Models\OrganizationMember;

class OrganizationOverviewService
{
    /**
     * Get comprehensive organization overview data
     */
    public function getOverviewData(Organization $organization): array
    {
        return [
            'bots' => $this->getBotStats($organization),
            'conversations' => $this->getConversationStats($organization),
            'members' => $this->getMemberStats($organization),
            //'storage' => $this->getStorageStats($organization),
           // 'tokens' => $this->getTokenStats($organization),
           // 'activity' => $this->getActivityStats($organization),
        ];
    }

    /**
     * Get bot statistics for the organization
     */
    private function getBotStats(Organization $organization): array
    {
        $organizationType = get_class($organization);
        
        $totalBots = Bot::where('owner_type', $organizationType)
            ->where('owner_id', $organization->id)
            ->count();

        $activeBots = Bot::where('owner_type', $organizationType)
            ->where('owner_id', $organization->id)
            ->where('status', 'active')
            ->count();

        $draftBots = Bot::where('owner_type', $organizationType)
            ->where('owner_id', $organization->id)
            ->where('status', 'draft')
            ->count();

        // Get most used bot
        $mostUsedBot = Bot::where('owner_type', $organizationType)
            ->where('owner_id', $organization->id)
            ->withCount('conversations')
            ->orderBy('conversations_count', 'desc')
            ->first();

        return [
            'total' => $totalBots,
            'active' => $activeBots,
            'draft' => $draftBots,
            'most_used_bot' => $mostUsedBot?->name,
        ];
    }

    /**
     * Get conversation statistics for the organization
     */
    private function getConversationStats(Organization $organization): array
    {
        $organizationType = get_class($organization);
        
        // Get all conversations for organization bots
        $botIds = Bot::where('owner_type', $organizationType)
            ->where('owner_id', $organization->id)
            ->pluck('id')
            ->toArray();

        $totalConversations = Conversation::whereIn('bot_id', $botIds)->count();

        $todayConversations = Conversation::whereIn('bot_id', $botIds)
            ->whereDate('created_at', Carbon::today())
            ->count();

        $weekConversations = Conversation::whereIn('bot_id', $botIds)
            ->whereBetween('created_at', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()])
            ->count();

        $monthConversations = Conversation::whereIn('bot_id', $botIds)
            ->whereMonth('created_at', Carbon::now()->month)
            ->whereYear('created_at', Carbon::now()->year)
            ->count();

        // Calculate average conversation length in minutes
        $avgLength = $this->calculateAverageConversationLength($botIds);

        return [
            'total' => $totalConversations,
            'today' => $todayConversations,
            'this_week' => $weekConversations,
            'this_month' => $monthConversations,
            'avg_conversation_length' => $avgLength,
        ];
    }

    /**
     * Get member statistics for the organization
     */
    private function getMemberStats(Organization $organization): array
    {
        $totalMembers = $organization->members()->count();
        
        // Active members (users who have logged in within last 30 days)
        $activeMembers = $organization->members()
            ->where('last_login_at', '>=', Carbon::now()->subDays(30))
            ->count();

        // Pending invitations
        
        $pendingInvitations = $organization->invitations()
            ->where('expires_at', '<', Carbon::now())
            ->count();

        $adminMembers = $organization->members()
            ->wherePivot('role', 'admin')
            ->count();

        return [
            'total' => $totalMembers,
            'active' => $activeMembers,
            'pending' => $pendingInvitations,
            'admins' => $adminMembers,
        ];
    }

    /**
     * Get storage statistics for the organization
     */
    private function getStorageStats(Organization $organization): array
    {
        $organizationType = get_class($organization);
        
        // Get storage from knowledge bases
        $knowledgeBases = KnowledgeBase::where('owner_type', $organizationType)
            ->where('owner_id', $organization->id)
            ->whereNotNull('metadata')
            ->get();

        $documentsSizeMB = 0;
        $attachmentsSizeMB = 0;

        foreach ($knowledgeBases as $kb) {
            $metadata = $kb->metadata ?? [];
            $fileSize = $metadata['file_size'] ?? 0;
            $documentsSizeMB += $fileSize / (1024 * 1024); // Convert bytes to MB
        }

        // For now, attachments size is 0 as we don't have attachment tracking yet
        // This can be extended when attachment functionality is implemented

        $totalUsedMB = $documentsSizeMB + $attachmentsSizeMB;
        
        // Get quota limit from organization settings or use default
        $quotaLimitMB = $organization->settings['storage_quota_mb'] ?? 5000; // 5GB default
        
        $remainingQuotaMB = max(0, $quotaLimitMB - $totalUsedMB);
        $usagePercent = $quotaLimitMB > 0 ? min(100, ($totalUsedMB / $quotaLimitMB) * 100) : 0;

        return [
            'total_used_mb' => round($totalUsedMB, 2),
            'documents_size_mb' => round($documentsSizeMB, 2),
            'attachments_size_mb' => round($attachmentsSizeMB, 2),
            'remaining_quota_mb' => round($remainingQuotaMB, 2),
            'quota_limit_mb' => $quotaLimitMB,
            'usage_percent' => round($usagePercent, 1),
        ];
    }

    /**
     * Get token usage statistics for the organization
     */
    private function getTokenStats(Organization $organization): array
    {
        $organizationType = get_class($organization);
        
        // Get all conversations for organization bots
        $botIds = Bot::where('owner_type', $organizationType)
            ->where('owner_id', $organization->id)
            ->pluck('id')
            ->toArray();

        $totalTokens = Message::whereHas('conversation', function($query) use ($botIds) {
                $query->whereIn('bot_id', $botIds);
            })
            ->sum('total_tokens') ?? 0;

        $monthlyTokens = Message::whereHas('conversation', function($query) use ($botIds) {
                $query->whereIn('bot_id', $botIds);
            })
            ->whereMonth('created_at', Carbon::now()->month)
            ->whereYear('created_at', Carbon::now()->year)
            ->sum('total_tokens') ?? 0;

        $estimatedCost = Message::whereHas('conversation', function($query) use ($botIds) {
                $query->whereIn('bot_id', $botIds);
            })
            ->sum('cost') ?? 0;

        return [
            'total_used' => $totalTokens,
            'this_month' => $monthlyTokens,
            'estimated_cost' => '$' . number_format($estimatedCost, 2),
        ];
    }

    /**
     * Get activity statistics for the organization
     */
    private function getActivityStats(Organization $organization): array
    {
        $organizationType = get_class($organization);
        
        // Get last activity time from latest conversation or message
        $botIds = Bot::where('owner_type', $organizationType)
            ->where('owner_id', $organization->id)
            ->pluck('id')
            ->toArray();

        $lastActivity = Conversation::whereIn('bot_id', $botIds)
            ->latest('last_message_at')
            ->first()?->last_message_at;

        // Get peak hours (hours with most conversations)
        $peakHours = $this->getPeakHours($botIds);

        // Get popular bots
        $popularBots = Bot::where('owner_type', $organizationType)
            ->where('owner_id', $organization->id)
            ->withCount('conversations')
            ->orderBy('conversations_count', 'desc')
            ->limit(5)
            ->get()
            ->map(function ($bot) {
                return [
                    'name' => $bot->name,
                    'usage' => $bot->conversations_count,
                ];
            })
            ->toArray();

        return [
            'last_active_at' => $lastActivity?->toISOString(),
            'peak_hours' => $peakHours,
            'popular_bots' => $popularBots,
        ];
    }

    /**
     * Calculate average conversation length in minutes
     */
    private function calculateAverageConversationLength(array $botIds): float
    {
        $conversations = Conversation::whereIn('bot_id', $botIds)
            ->whereNotNull('last_message_at')
            ->get(['created_at', 'last_message_at']);

        if ($conversations->isEmpty()) {
            return 0;
        }

        $totalMinutes = $conversations->sum(function ($conversation) {
            return $conversation->created_at->diffInMinutes($conversation->last_message_at);
        });

        return round($totalMinutes / $conversations->count(), 1);
    }

    /**
     * Get peak hours based on conversation creation times
     */
    private function getPeakHours(array $botIds): array
    {
        $hourCounts = Conversation::whereIn('bot_id', $botIds)
            ->selectRaw('HOUR(created_at) as hour, COUNT(*) as count')
            ->groupBy('hour')
            ->orderBy('count', 'desc')
            ->limit(3)
            ->pluck('hour')
            ->toArray();

        return $hourCounts;
    }
}
