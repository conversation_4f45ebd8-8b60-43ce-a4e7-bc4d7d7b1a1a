<?php

namespace Modules\Organization\Http\Requests\Organization;

use Modules\Core\Http\Requests\BaseFormRequest;

class OverviewRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization is handled in the controller
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            // No additional validation needed for overview endpoint
            // UUID validation is handled by route model binding
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            // No custom messages needed
        ];
    }
}
