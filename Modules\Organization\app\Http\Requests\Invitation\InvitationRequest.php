<?php

namespace Modules\Organization\Http\Requests\Invitation;

use Modules\Core\Http\Requests\BaseFormRequest;
use Modules\Organization\Traits\OrganizationAuthorization;
use Illuminate\Validation\Rule;

class InvitationRequest extends BaseFormRequest
{
    use OrganizationAuthorization;

    public function rules(): array
    {
        return [
            'email' => [
                'required',
                'email',
                'max:255',
                'exists:users,email'
            ],
            'role' => [
                'required',
                'string',
                Rule::in(['admin', 'editor', 'viewer', 'member', 'guest']),
            ],
            'message' => 'nullable|string|max:500',
        ];
    }

    public function authorize(): bool
    {
        return $this->hasOrganizationAdminAccess();
    }
}
