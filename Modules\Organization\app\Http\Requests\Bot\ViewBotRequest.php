<?php

namespace Modules\Organization\Http\Requests\Bot;

use Modules\Core\Http\Requests\BaseFormRequest;
use Modules\Organization\Traits\OrganizationAuthorization;

class ViewBotRequest extends BaseFormRequest
{
    use OrganizationAuthorization;

    public function rules(): array
    {
        return [
            'limit' => 'nullable|integer|min:1|max:100',
            'page' => 'nullable|integer|min:1',
            'search' => 'nullable|string|max:255',
            'status' => 'nullable|string|in:active,inactive',
            'visibility' => 'nullable|string|in:public,private',
            'model' => 'nullable|string|exists:model_ais,key',
            'created_from' => 'nullable|date',
            'created_to' => 'nullable|date|after_or_equal:created_from'
        ];
    }

    public function authorize(): bool
    {
        return $this->hasOrganizationAccess();
    }
}
