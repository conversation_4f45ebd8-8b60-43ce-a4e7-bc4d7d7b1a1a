<?php

namespace Modules\Organization\Http\Controllers\Auth;

use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use Modules\Core\Traits\ResponseTrait;
use Modules\Organization\Http\Requests\Organization\SettingsRequest;
use Modules\Organization\Traits\OrganizationAuthorization;

class OrganizationSettingsController extends Controller
{
    use ResponseTrait, OrganizationAuthorization;

    public function show(string $orgUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        
        $settings = [
            'invitation' => [
                'default_role' => $organization->getDefaultMemberRole(),
                'expiry_days' => $organization->getInvitationExpiryDays(),
                'auto_accept' => $organization->getSetting('invitation.auto_accept', false),
                'allow_member_invite_guests' => $organization->getSetting('invitation.allow_member_invite_guests', false),
            ],
            'notifications' => [
                'enabled' => $organization->areNotificationsEnabled(),
                'email_invitations' => $organization->getSetting('notifications.email_invitations', true),
                'email_role_changes' => $organization->getSetting('notifications.email_role_changes', true),
                'email_member_removed' => $organization->getSetting('notifications.email_member_removed', true),
            ],
            'bot_access' => [
                'default_visibility' => $organization->getSetting('bot_access.default_visibility', 'organization'),
                'allow_private_bots' => $organization->getSetting('bot_access.allow_private_bots', true),
                'auto_assign_new_members' => $organization->getSetting('bot_access.auto_assign_new_members', false),
            ],
            'knowledge_base' => [
                'admin_only' => $organization->getSetting('knowledge_base.admin_only', true),
                'auto_share_with_bots' => $organization->getSetting('knowledge_base.auto_share_with_bots', false),
            ]
        ];

        return $this->successResponse($settings, __('Organization settings retrieved successfully.'));
    }

    public function update(SettingsRequest $request, string $orgUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        
        // Only Owner/Admin can update settings
        if (!$this->hasOrganizationOwnerAccess()) {
            return $this->errorResponse(null, __('Only organization owners can update settings.'), 403);
        }
        
        $settings = $request->validated();
        
        // Update invitation settings
        if (isset($settings['invitation'])) {
            foreach ($settings['invitation'] as $key => $value) {
                $organization->setSetting("invitation.{$key}", $value);
            }
        }
        
        // Update notification settings
        if (isset($settings['notifications'])) {
            foreach ($settings['notifications'] as $key => $value) {
                $organization->setSetting("notifications.{$key}", $value);
            }
        }
        
        // Update bot access settings
        if (isset($settings['bot_access'])) {
            foreach ($settings['bot_access'] as $key => $value) {
                $organization->setSetting("bot_access.{$key}", $value);
            }
        }
        
        // Update knowledge base settings
        if (isset($settings['knowledge_base'])) {
            foreach ($settings['knowledge_base'] as $key => $value) {
                $organization->setSetting("knowledge_base.{$key}", $value);
            }
        }

        return $this->successResponse(null, __('Organization settings updated successfully.'));
    }

    public function resetToDefaults(string $orgUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        
        // Only Owner can reset settings
        if (!$this->hasOrganizationOwnerAccess()) {
            return $this->errorResponse(null, __('Only organization owners can reset settings.'), 403);
        }
        
        $organization->settings = null;
        $organization->save();

        return $this->successResponse(null, __('Organization settings reset to defaults successfully.'));
    }
}
