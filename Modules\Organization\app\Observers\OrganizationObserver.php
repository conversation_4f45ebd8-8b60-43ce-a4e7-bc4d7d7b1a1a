<?php

namespace Modules\Organization\Observers;

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Modules\Organization\Models\Organization;

class OrganizationObserver
{
    /**
     * Handle the Organization "creating" event.
     */
    public function creating(Organization $organization): void
    {
        // Set owner if not provided
        if (!$organization->owner_id) {
            $organization->owner_id = auth()->id();
        }

        // Generate UUID
        $this->generateUuid($organization);

        // Set hierarchical path and depth
        $this->setPathAndDepth($organization);
    }

    /**
     * Handle the Organization "updating" event.
     */
    public function updating(Organization $organization): void
    {
        // Update path and depth if parent_id changed
        if ($organization->isDirty('parent_id')) {
            $this->setPathAndDepth($organization);
        }
    }

    /**
     * Handle the Organization "created" event.
     */
    public function created(Organization $organization): void
    {
        if ($organization->logo) {
            $newLogo = $this->moveLogoFromTemp($organization->logo, $organization->uuid);
            if ($newLogo !== $organization->logo) {
                $organization->updateQuietly(['logo' => $newLogo]);
            }
        }
    }

    /**
     * Handle the Organization "updated" event.
     */
    public function updated(Organization $organization): void
    {
        // Handle logo movement
        if ($organization->isDirty('logo') && $organization->logo) {
            $newLogo = $this->moveLogoFromTemp($organization->logo, $organization->uuid);
            if ($newLogo !== $organization->logo) {
                $organization->updateQuietly(['logo' => $newLogo]);
            }
        }

        // Update children paths when parent path changes
        if ($organization->isDirty('path')) {
            $organization->updateChildrenPaths();
        }
    }

    /**
     * Handle the Organization "deleted" event.
     */
    public function deleted(Organization $organization): void
    {
        // Add any cleanup logic for soft deletes if needed
    }

    /**
     * Handle the Organization "restored" event.
     */
    public function restored(Organization $organization): void
    {
        // Add any restoration logic if needed
    }

    /**
     * Handle the Organization "force deleted" event.
     */
    public function forceDeleted(Organization $organization): void
    {
        // Clean up logo file when organization is permanently deleted
        if ($organization->logo) {
            Storage::disk('public')->delete($organization->logo);
        }
    }

    // ========================================
    // Private Helper Methods
    // ========================================

    /**
     * Generate UUID for the organization if not provided.
     */
    private function generateUuid(Organization $organization): void
    {
        if (empty($organization->uuid)) {
            $organization->uuid = Str::uuid();
        }
    }

    /**
     * Set path and depth for hierarchical structure.
     */
    private function setPathAndDepth(Organization $organization): void
    {
        if ($organization->parent_id) {
            $parent = Organization::find($organization->parent_id);
            if ($parent) {
                $organization->path = $parent->path
                    ? $parent->path . '/' . $parent->id
                    : (string)$parent->id;
                $organization->depth = ($parent->depth ?? 0) + 1;
            } else {
                $organization->path = null;
                $organization->depth = 0;
            }
        } else {
            // Root organization
            $organization->path = null;
            $organization->depth = 0;
        }
    }

    /**
     * Move logo from temporary to organization directory.
     */
    private function moveLogoFromTemp(?string $tempPath, string $uuid): ?string
    {
        if (!$tempPath || !Storage::disk('public')->exists($tempPath)) {
            return $tempPath;
        }

        if (!Str::contains($tempPath, '/temp/')) {
            return $tempPath;
        }

        $filename = basename($tempPath);
        $newPath = "logos/organizations/{$uuid}/{$filename}";

        if (Storage::disk('public')->move($tempPath, $newPath)) {
            return $newPath;
        }

        return $tempPath;
    }
}
