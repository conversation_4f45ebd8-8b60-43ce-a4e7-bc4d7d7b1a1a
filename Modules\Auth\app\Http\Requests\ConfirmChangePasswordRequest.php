<?php

namespace Modules\Auth\Http\Requests;

use Modules\Core\Http\Requests\BaseFormRequest;

class ConfirmChangePasswordRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'otp' => [
                'required',
                'string',
                'size:6',
            ],
            'password' => [
                'required',
                'string',
                'min:8',
                'confirmed',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'code.required' => __('Verification code is required.'),
            'code.size' => __('Verification code must be 6 digits.'),
            'password.required' => __('New password is required.'),
            'password.min' => __('New password must be at least 8 characters.'),
            'password.confirmed' => __('New password confirmation does not match.'),
        ];
    }
}
