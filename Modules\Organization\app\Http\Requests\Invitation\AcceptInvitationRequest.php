<?php

namespace Modules\Organization\Http\Requests\Invitation;

use Modules\Core\Http\Requests\BaseFormRequest;

class AcceptInvitationRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'token' => 'required|string|size:64',
            'action' => 'required|string|in:accept,decline'
        ];
    }

    public function authorize(): bool
    {
        // Public endpoint - no organization authorization needed
        return true;
    }
}
