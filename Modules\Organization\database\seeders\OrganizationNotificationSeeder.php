<?php

namespace Modules\Organization\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class OrganizationNotificationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Seeding Organization notification types and templates...');
        
        $this->seedNotificationTypes();
        $this->seedNotificationTemplates();
        
        $this->command->info('Organization notification seeding completed!');
    }

    /**
     * Seed Organization-related notification types.
     */
    private function seedNotificationTypes(): void
    {
        $notificationTypes = [
            [
                'key' => 'organization.member.invited',
                'name' => 'Organization Member Invitation',
                'description' => 'Notification sent when user is invited to join organization as member',
                'channels' => json_encode(['database', 'email']),
                'default_settings' => json_encode([
                    'priority' => 'high',
                    'auto_send' => true,
                    'retry_attempts' => 3,
                    'delay_seconds' => 0,
                ]),
                'is_system' => true,
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'organization.guest.invited',
                'name' => 'Organization Guest Invitation',
                'description' => 'Notification sent when user is invited to join organization as guest',
                'channels' => json_encode(['database', 'email']),
                'default_settings' => json_encode([
                    'priority' => 'high',
                    'auto_send' => true,
                    'retry_attempts' => 3,
                    'delay_seconds' => 0,
                ]),
                'is_system' => true,
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'organization.invitation.accepted',
                'name' => 'Organization Invitation Accepted',
                'description' => 'Notification sent to inviter when invitation is accepted',
                'channels' => json_encode(['database', 'email']),
                'default_settings' => json_encode([
                    'priority' => 'medium',
                    'auto_send' => true,
                    'retry_attempts' => 2,
                    'delay_seconds' => 0,
                ]),
                'is_system' => true,
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'organization.invitation.declined',
                'name' => 'Organization Invitation Declined',
                'description' => 'Notification sent to inviter when invitation is declined',
                'channels' => json_encode(['database', 'email']),
                'default_settings' => json_encode([
                    'priority' => 'low',
                    'auto_send' => true,
                    'retry_attempts' => 1,
                    'delay_seconds' => 0,
                ]),
                'is_system' => true,
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'organization.member.role_changed',
                'name' => 'Organization Member Role Changed',
                'description' => 'Notification sent when member role is updated',
                'channels' => json_encode(['database', 'email']),
                'default_settings' => json_encode([
                    'priority' => 'medium',
                    'auto_send' => true,
                    'retry_attempts' => 2,
                    'delay_seconds' => 0,
                ]),
                'is_system' => true,
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'organization.member.removed',
                'name' => 'Organization Member Removed',
                'description' => 'Notification sent when member is removed from organization',
                'channels' => json_encode(['database', 'email']),
                'default_settings' => json_encode([
                    'priority' => 'medium',
                    'auto_send' => true,
                    'retry_attempts' => 2,
                    'delay_seconds' => 0,
                ]),
                'is_system' => true,
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'organization.bot.access_granted',
                'name' => 'Organization Bot Access Granted',
                'description' => 'Notification sent when user is granted access to private bot',
                'channels' => json_encode(['database', 'email']),
                'default_settings' => json_encode([
                    'priority' => 'medium',
                    'auto_send' => true,
                    'retry_attempts' => 2,
                    'delay_seconds' => 0,
                ]),
                'is_system' => true,
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'organization.bot.access_revoked',
                'name' => 'Organization Bot Access Revoked',
                'description' => 'Notification sent when user access to private bot is revoked',
                'channels' => json_encode(['database', 'email']),
                'default_settings' => json_encode([
                    'priority' => 'low',
                    'auto_send' => true,
                    'retry_attempts' => 1,
                    'delay_seconds' => 0,
                ]),
                'is_system' => true,
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        foreach ($notificationTypes as $type) {
            DB::table('notification_types')->updateOrInsert(
                ['key' => $type['key']],
                $type
            );
        }

        $this->command->info('Organization notification types seeded successfully.');
    }

    /**
     * Seed Organization-related notification templates.
     */
    private function seedNotificationTemplates(): void
    {
        // Get notification types
        $notificationTypes = DB::table('notification_types')
            ->whereIn('key', [
                'organization.member.invited', 'organization.guest.invited',
                'organization.invitation.accepted', 'organization.invitation.declined',
                'organization.member.role_changed', 'organization.member.removed',
                'organization.bot.access_granted', 'organization.bot.access_revoked'
            ])
            ->get()
            ->keyBy('key');

        $templates = [];

        $this->command->info('Creating Organization notification templates...');

        // Member Invitation Templates
        if (isset($notificationTypes['organization.member.invited'])) {
            $memberInvitedType = $notificationTypes['organization.member.invited'];

            // English database template
            $templates[] = [
                'notification_type_id' => $memberInvitedType->id,
                'channel' => 'database',
                'locale' => 'en',
                'subject' => null,
                'title' => 'Invitation to join {organization_name}',
                'content' => 'You have been invited by {inviter_name} to join {organization_name} as {role}.',
                'variables' => json_encode(['user_name', 'organization_name', 'inviter_name', 'role', 'accept_url', 'decline_url']),
                'settings' => json_encode([]),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];

            // English email template
            $templates[] = [
                'notification_type_id' => $memberInvitedType->id,
                'channel' => 'email',
                'locale' => 'en',
                'subject' => 'Invitation to join {organization_name} - {app_name}',
                'title' => 'Organization Invitation',
                'content' => '<!DOCTYPE html>
                    <html>
                    <head>
                        <meta charset="utf-8">
                        <meta name="viewport" content="width=device-width, initial-scale=1.0">
                        <title>Organization Invitation - {app_name}</title>
                    </head>
                    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
                        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
                            <h1 style="color: white; margin: 0; font-size: 28px;">🏢 Organization Invitation</h1>
                        </div>
                        
                        <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;">
                            <p style="font-size: 16px; margin-bottom: 20px;">Hello <strong>{user_name}</strong>,</p>
                            
                            <p style="font-size: 16px; margin-bottom: 20px;">
                                <strong>{inviter_name}</strong> has invited you to join <strong>{organization_name}</strong> as a <strong>{role}</strong>.
                            </p>
                            
                            <div style="text-align: center; margin: 30px 0;">
                                <a href="{accept_url}" style="background: #28a745; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block; margin-right: 10px;">
                                    Accept Invitation
                                </a>
                                <a href="{decline_url}" style="background: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
                                    Decline
                                </a>
                            </div>
                            
                            <p style="font-size: 14px; color: #666; margin-top: 20px;">
                                This invitation will expire in 7 days. If you did not expect this invitation, you can safely ignore this email.
                            </p>
                            
                            <hr style="border: none; border-top: 1px solid #e9ecef; margin: 30px 0;">
                            
                            <p style="font-size: 14px; color: #666; text-align: center; margin: 0;">
                                Best regards,<br>
                                The {app_name} Team
                            </p>
                        </div>
                    </body>
                    </html>',
                'variables' => json_encode(['user_name', 'organization_name', 'inviter_name', 'role', 'accept_url', 'decline_url', 'app_name']),
                'settings' => json_encode(['from_name' => '{app_name} Team']),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        // Insert all templates
        foreach ($templates as $template) {
            DB::table('notification_templates')->updateOrInsert(
                [
                    'notification_type_id' => $template['notification_type_id'],
                    'channel' => $template['channel'],
                    'locale' => $template['locale']
                ],
                $template
            );
        }

        $this->command->info('Organization notification templates seeded successfully.');
    }
}
