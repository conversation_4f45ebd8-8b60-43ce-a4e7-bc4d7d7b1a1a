<?php

namespace Modules\User\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Modules\Location\Models\Country;
use Modules\Location\Models\GeoDivision;
use Modules\User\Enums\UserGender;
use Modules\User\Enums\UserStatus;
use Modules\User\Models\User;

/**
 * @extends Factory<User>
 */
class UserFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = User::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'uuid' => (string) Str::uuid(),
            'username' => $this->faker->unique()->userName(),
            'password' => Hash::make('password'),
            'first_name' => $this->faker->firstName(),
            'last_name' => $this->faker->lastName(),
            'avatar' => null,
            'birthday' => $this->faker->optional(0.7)->dateTimeBetween('-80 years', '-18 years'),
            'gender' => $this->faker->randomElement(UserGender::values()),
            'email' => $this->faker->unique()->safeEmail(),
            'email_verified_at' => $this->faker->optional(0.8)->dateTimeBetween('-1 year', 'now'),
            'phone' => $this->faker->optional(0.6)->phoneNumber(),
            'phone_verified_at' => null,
            'address' => $this->faker->optional(0.7)->address(),
            // Temporarily comment out for testing
            // 'geo_division_id' => null,
            // 'country_id' => null,
            'status' => $this->faker->randomElement(UserStatus::values()),
            'last_login_at' => $this->faker->optional(0.6)->dateTimeBetween('-30 days', 'now'),
            'last_login_ip' => $this->faker->optional(0.6)->ipv4(),
            'preferences' => $this->faker->optional(0.4)->randomElements([
                'theme' => $this->faker->randomElement(['light', 'dark']),
                'language' => $this->faker->randomElement(['en', 'vi', 'fr']),
                'timezone' => $this->faker->timezone(),
                'notifications' => [
                    'email' => $this->faker->boolean(),
                    'sms' => $this->faker->boolean(),
                    'push' => $this->faker->boolean(),
                ],
            ]),
            'is_verified' => $this->faker->boolean(70),
            'newsletter_subscribed' => $this->faker->boolean(30),
            'remember_token' => Str::random(10),
        ];
    }

    /**
     * Indicate that the user is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => UserStatus::Active,
        ]);
    }

    /**
     * Indicate that the user is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => UserStatus::Inactive,
        ]);
    }

    /**
     * Indicate that the user is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => UserStatus::Pending,
        ]);
    }

    /**
     * Indicate that the user is banned.
     */
    public function banned(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => UserStatus::Banned,
        ]);
    }

    /**
     * Indicate that the user is suspended.
     */
    public function suspended(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => UserStatus::Suspended,
        ]);
    }

    /**
     * Indicate that the user is verified.
     */
    public function verified(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_verified' => true,
            'email_verified_at' => now(),
        ]);
    }

    /**
     * Indicate that the user is unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_verified' => false,
            'email_verified_at' => null,
        ]);
    }

    /**
     * Indicate that the user is male.
     */
    public function male(): static
    {
        return $this->state(fn (array $attributes) => [
            'gender' => UserGender::Male,
            'first_name' => $this->faker->firstNameMale(),
        ]);
    }

    /**
     * Indicate that the user is female.
     */
    public function female(): static
    {
        return $this->state(fn (array $attributes) => [
            'gender' => UserGender::Female,
            'first_name' => $this->faker->firstNameFemale(),
        ]);
    }

    /**
     * Indicate that the user has phone verified.
     */
    public function phoneVerified(): static
    {
        return $this->state(fn (array $attributes) => [
            'phone' => $this->faker->phoneNumber(),
            'phone_verified_at' => now(),
        ]);
    }

    /**
     * Indicate that the user subscribes to newsletter.
     */
    public function newsletterSubscribed(): static
    {
        return $this->state(fn (array $attributes) => [
            'newsletter_subscribed' => true,
        ]);
    }

    /**
     * Indicate that the user has recently logged in.
     */
    public function recentlyLoggedIn(): static
    {
        return $this->state(fn (array $attributes) => [
            'last_login_at' => $this->faker->dateTimeBetween('-7 days', 'now'),
            'last_login_ip' => $this->faker->ipv4(),
        ]);
    }

    /**
     * Indicate that the user has a country.
     */
    public function withCountry(int $countryId = 1): static
    {
        return $this->state(fn (array $attributes) => [
            'country_id' => $countryId,
        ]);
    }

    /**
     * Indicate that the user has a geographic division.
     */
    public function withGeoDivision(int $geoDivisionId = 1): static
    {
        return $this->state(fn (array $attributes) => [
            'geo_division_id' => $geoDivisionId,
        ]);
    }
}
