<?php

namespace Modules\Organization\Http\Requests\Invitation;

use Modules\Core\Http\Requests\BaseFormRequest;
use Modules\Organization\Traits\OrganizationAuthorization;

class BulkInvitationRequest extends BaseFormRequest
{
    use OrganizationAuthorization;

    public function rules(): array
    {
        return [
            'ids' => 'required|array|min:1',
            'ids.*' => 'integer|exists:organization_invitations,id',
        ];
    }

    public function authorize(): bool
    {
        return $this->hasOrganizationAdminAccess();
    }
}
