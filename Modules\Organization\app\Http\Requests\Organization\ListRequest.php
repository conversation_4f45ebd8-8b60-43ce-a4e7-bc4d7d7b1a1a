<?php

namespace Modules\Organization\Http\Requests\Organization;

use Modules\Core\Http\Requests\BaseFormRequest;
use Modules\Organization\Models\Organization;

class ListRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'limit' => 'nullable|integer|min:1|max:100',
            'page' => 'nullable|integer|min:1',
            'search' => 'nullable|string|max:255',
            'type' => 'nullable|string|in:company,team',
            'status' => 'nullable|string|in:active,inactive',
            'visibility' => 'nullable|string|in:private,internal,public',
            'owner_id' => 'nullable|integer|exists:users,id',
            'parent_id' => 'nullable|integer|exists:organizations,id',
            'country_id' => 'nullable|integer|exists:countries,id',
            'sort_by' => 'nullable|string|in:name,created_at,updated_at,type,status',
            'sort_direction' => 'nullable|string|in:asc,desc'
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $user = $this->user();

        if (!$user) {
            return false;
        }

        // Use Policy to check if user can view organizations
        return $user->can('viewAny', Organization::class);
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'limit.integer' => __('Limit must be a number.'),
            'limit.min' => __('Limit must be at least 1.'),
            'limit.max' => __('Limit cannot exceed 100.'),
            'page.integer' => __('Page must be a number.'),
            'page.min' => __('Page must be at least 1.'),
            'search.max' => __('Search term cannot exceed 255 characters.'),
            'type.in' => __('Invalid type. Available types: company, team.'),
            'status.in' => __('Invalid status. Available statuses: active, inactive.'),
            'visibility.in' => __('Invalid visibility. Available options: private, internal, public.'),
            'owner_id.exists' => __('The selected owner does not exist.'),
            'parent_id.exists' => __('The selected parent organization does not exist.'),
            'country_id.exists' => __('The selected country does not exist.'),
            'sort_by.in' => __('Invalid sort field. Available fields: name, created_at, updated_at, type, status.'),
            'sort_direction.in' => __('Sort direction must be asc or desc.'),
            'is_trashed.boolean' => __('Is trashed must be true or false.'),
        ];
    }
}
