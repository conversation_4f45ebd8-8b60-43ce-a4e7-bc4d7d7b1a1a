<?php

namespace Modules\Organization\Http\Requests\Organization;

use Modules\Core\Http\Requests\BaseFormRequest;
use Modules\Organization\Traits\OrganizationAuthorization;

class SettingsRequest extends BaseFormRequest
{
    use OrganizationAuthorization;

    public function rules(): array
    {
        return [
            'invitation' => 'nullable|array',
            'invitation.default_role' => 'nullable|string|in:admin,editor,viewer,member,guest',
            'invitation.expiry_days' => 'nullable|integer|min:1|max:30',
            'invitation.auto_accept' => 'nullable|boolean',
            'invitation.allow_member_invite_guests' => 'nullable|boolean',
            
            'notifications' => 'nullable|array',
            'notifications.enabled' => 'nullable|boolean',
            'notifications.email_invitations' => 'nullable|boolean',
            'notifications.email_role_changes' => 'nullable|boolean',
            'notifications.email_member_removed' => 'nullable|boolean',
            
            'bot_access' => 'nullable|array',
            'bot_access.default_visibility' => 'nullable|string|in:public,organization,private',
            'bot_access.allow_private_bots' => 'nullable|boolean',
            'bot_access.auto_assign_new_members' => 'nullable|boolean',
            
            'knowledge_base' => 'nullable|array',
            'knowledge_base.admin_only' => 'nullable|boolean',
            'knowledge_base.auto_share_with_bots' => 'nullable|boolean',
        ];
    }

    public function authorize(): bool
    {
        return $this->hasOrganizationOwnerAccess();
    }
}
