<?php

namespace Modules\Organization\Http\Requests\Invitation;

use Modules\Core\Http\Requests\BaseFormRequest;
use Modules\Organization\Traits\OrganizationAuthorization;

class DeleteInvitationRequest extends BaseFormRequest
{
    use OrganizationAuthorization;

    public function rules(): array
    {
        return [];
    }

    public function authorize(): bool
    {
        return $this->hasOrganizationAdminAccess();
    }
}
