<?php

namespace Modules\Organization\Http\Requests\Guest;

use Modules\Core\Http\Requests\BaseFormRequest;
use Modules\Organization\Traits\OrganizationAuthorization;

class GuestRequest extends BaseFormRequest
{
    use OrganizationAuthorization;

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $rules = [];

        // For store (POST) - require user_id or email
        if ($this->isMethod('POST')) {
            $rules['user_id'] = [
                'required',
                'integer',
                'exists:users,id'
            ];
        }

        return $rules;
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->hasOrganizationAdminAccess();
    }
}
