<?php

namespace Modules\Organization\Http\Requests\Member;

use Modules\Core\Http\Requests\BaseFormRequest;
use Modules\Organization\Traits\OrganizationAuthorization;
use Illuminate\Validation\Rule;

class BulkMemberRequest extends BaseFormRequest
{
    use OrganizationAuthorization;
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $rules = [
            'ids' => [
                'required',
                'array',
                'min:1',
            ],
            'ids.*' => [
                'required',
                'string',
                'exists:users,uuid',
            ],
        ];

        // For bulk update role
        if ($this->isMethod('PATCH') && $this->has('role')) {
            $rules['role'] = [
                'required',
                'string',
                Rule::in(['admin', 'editor', 'viewer', 'member']),
            ];
        }

        return $rules;
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->hasOrganizationAdminAccess();
    }
}
