# Available Users API Endpoint

## Overview

The Available Users API endpoint provides a way to retrieve users who are not currently members of any organization. This is particularly useful for invitation systems where you need to find users who can be invited to join organizations.

## Endpoint Details

**URL:** `GET /api/v1/auth/users/available`

**Authentication:** Required (<PERSON><PERSON>)

**Authorization:** Users with `admin` or `super-admin` roles

## Request Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `limit` | integer | No | Number of results per page (1-100, default: 10) |
| `page` | integer | No | Page number (default: 1) |
| `search` | string | No | General search term (searches name, email, phone) |
| `email` | string | No | Filter by email address (partial match) |
| `name` | string | No | Filter by name (partial match on first/last name) |
| `phone` | string | No | Filter by phone number (partial match) |

## Response Format

### Success Response (200)

```json
{
    "success": true,
    "message": "Available users retrieved successfully.",
    "data": [
        {
            "uuid": "123e4567-e89b-12d3-a456-************",
            "first_name": "John",
            "last_name": "Doe",
            "full_name": "John Doe",
            "email": "<EMAIL>",
            "phone": "+1234567890",
            "avatar": "https://example.com/avatar.jpg",
            "status": "active"
        }
    ],
    "total": 25,
    "limit": 10
}
```

### Error Responses

**401 Unauthorized**
```json
{
    "message": "Unauthenticated."
}
```

**403 Forbidden**
```json
{
    "message": "This action is unauthorized."
}
```

**422 Validation Error**
```json
{
    "message": "The given data was invalid.",
    "errors": {
        "limit": ["The limit must be between 1 and 100."]
    }
}
```

## Usage Examples

### Basic Request
```bash
curl -X GET "https://your-domain.com/api/v1/auth/users/available" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Accept: application/json"
```

### Search by Name
```bash
curl -X GET "https://your-domain.com/api/v1/auth/users/available?search=John" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Accept: application/json"
```

### Filter by Email
```bash
curl -X GET "https://your-domain.com/api/v1/auth/users/available?email=<EMAIL>" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Accept: application/json"
```

### Pagination
```bash
curl -X GET "https://your-domain.com/api/v1/auth/users/available?page=2&limit=20" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Accept: application/json"
```

## Data Scope

The endpoint returns users who meet ALL of the following criteria:

1. **Not in any organization**: Users who are not members of any organization (no records in `organization_members` table)
2. **Active status**: Only users with `status = 'active'`
3. **Match search criteria**: If search parameters are provided, users must match the specified filters

## Security Considerations

- Only authenticated users can access this endpoint
- Only users with `admin` or `super-admin` roles are authorized
- Sensitive information like passwords are excluded from the response
- Results are paginated to prevent large data dumps

## Integration Notes

This endpoint is designed to work with invitation systems where:

1. Administrators need to find users to invite to organizations
2. The system should only show users who are not already organization members
3. Search functionality helps find specific users quickly
4. Pagination handles large user bases efficiently

## Related Endpoints

- `POST /api/v1/auth/organizations/{orgUuid}/invitations` - Send invitations to users
- `GET /api/v1/auth/organizations/{orgUuid}/members` - List current organization members
- `GET /api/v1/auth/organizations/{orgUuid}/invitations` - List pending invitations
