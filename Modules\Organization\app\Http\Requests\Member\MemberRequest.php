<?php

namespace Modules\Organization\Http\Requests\Member;

use Modules\Core\Http\Requests\BaseFormRequest;
use Modules\Organization\Traits\OrganizationAuthorization;
use Illuminate\Validation\Rule;

class MemberRequest extends BaseFormRequest
{
    use OrganizationAuthorization;
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'invites'   => ['nullable', 'array', 'min:1'],

            'invites.*' => ['required', 'array'],

            'invites.*.uuid' => ['required', 'string', 'exists:users,uuid'],

            'invites.*.role' => ['required', 'string', Rule::in(['admin', 'editor', 'member'])],
            
            'message' => ['nullable', 'string', 'max:500'],
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->hasOrganizationAdminAccess();
    }
}
