<?php

namespace Modules\ChatBot\Http\Controllers\Auth;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\UploadedFile;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Log;
use Modules\ChatBot\Http\Filters\BotFilter;
use Modules\ChatBot\Http\Requests\BotRequest;
use Modules\ChatBot\Jobs\RAGFileProcessingJob;
use Modules\ChatBot\Models\Bot;
use Modules\ChatBot\Models\KnowledgeBase;
use Modules\Core\Traits\ResponseTrait;
use Modules\ModelAI\Facades\ModelAIFacade;
use Modules\Organization\Models\Organization;
use Modules\User\Models\User;
use Throwable;

class BotController extends Controller
{
    use ResponseTrait;

    public function __construct()
    {
        $this->middleware('role_or_permission:super-admin|chatbot.bot.view')->only(['index', 'show', 'dropdown']);
        $this->middleware('role_or_permission:super-admin|chatbot.bot.create')->only(['store']);
        $this->middleware('role_or_permission:super-admin|chatbot.bot.edit')->only(['update']);
        $this->middleware('role_or_permission:super-admin|chatbot.bot.delete')->only(['delete', 'restore', 'bulkDelete', 'bulkRestore']);
        $this->middleware('role_or_permission:super-admin|chatbot.bot.destroy')->only(['destroy', 'bulkDestroy']);
    }

    /**
     * Display a listing of user's accessible bots.
     */
    public function index(Request $request): JsonResponse
    {
        $user = auth()->user();

        $bots = Bot::query()
            ->with([
                'aiModel:id,key,name',
                'aiModel.services' => function ($query) {
                    $query->select(['id', 'model_ai_id', 'allowed_parameters', 'default_parameters'])
                        ->where('status', 'active');
                },
                'owner:id,first_name,last_name,full_name,avatar',
                'knowledgeBases:id,uuid,name,type,status,storage_path,metadata'
            ])
            ->filter(new BotFilter($request))
            ->where([
                'owner_id' => $user->id,
                'owner_type' => get_class($user)
            ])
            ->orderByDesc('updated_at')
            ->paginate($request->input('limit', 10))
            ->through(function ($bot) {
                $bot->makeVisible(['system_prompt'])->append(['logo_url']);
                // Add botFiles to each bot
                $bot->knowledge = [
                    'enabled' => true,
                    'text' => null,
                    'newUploads' => [],
                    'libraryFiles' => $bot->knowledgeBases->pluck('uuid')
                ];
                // Hide knowledgeBases relation to avoid duplication
                $bot->makeHidden(['knowledgeBases']);

                return $bot;
            });
        return $this->paginatedResponse($bots, __('Bots retrieved successfully.'));
    }

    /**
     * Store a newly created bot.
     * @throws Throwable
     */
    public function store(BotRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $user = auth()->user();

            $data = $request->except(['knowledge_sources']);
            $data['owner_id'] = $user->id;
            $data['owner_type'] = get_class($user);
            $data['model_ai_id'] = ModelAIFacade::getModelByKey($request->input('model'))?->id;
            $data['status'] = 'active';

            $knowledgeData = $request->input('knowledge', []);

            $data['metadata'] = [
                'rag_config' => [
                    'enabled' => $knowledgeData['enabled'] ?? true,
                    'auto_query' => $knowledgeData['auto_query'] ?? false,
                    'top_k' => $knowledgeData['top_k'] ?? 5,
                    'collection' => 'documents'
                ]
            ];
            // Create bot
            $bot = Bot::create($data);

            // Process knowledge sources
            $pendingKnowledgeBaseIds = $this->processKnowledgeSources($request, $bot);

            // Dispatch RAG processing job if there are pending files
            if (!empty($pendingKnowledgeBaseIds)) {
                RAGFileProcessingJob::dispatch(
                    $pendingKnowledgeBaseIds,
                    $user->id,
                    get_class($user),
                    'bot_creation',
                    $bot->id
                );
            }

            $bot->load(['aiModel:id,key,name', 'owner:id,first_name,last_name,full_name,avatar']);

            DB::commit();

            return $this->successResponse($bot, __('Bot created successfully.'), 201);

        } catch (Exception $e) {
            DB::rollBack();
            return $this->safeErrorResponse($e, __('Failed to create bot.'));
        }
    }

    /**
     * Display the specified bot.
     */
    public function show(string $id): JsonResponse
    {
        try {
            $user = auth()->user();
            $bot = Bot::with(['aiModel:id,key,name', 'owner:id,first_name,last_name,full_name,avatar'])
                ->where('uuid', $id)->firstOrFail();

            // Check if user can access this bot
            if (!$bot->userCan($user->id, 'view')) {
                return $this->errorResponse(__('Access denied.'), 403);
            }

            $bot->knowledge = [
                "enabled" => true,
                "new_uploads" => [],
                "library_files" => $bot->knowledgeBases->pluck('uuid')->toArray(),
                "bot_files" => $bot->knowledgeBases,
                "text" => null
            ];

            // Hide knowledge bases relation
            $bot->makeHidden(['knowledgeBases']);

            return $this->successResponse($bot, __('Bot retrieved successfully.'));

        } catch (Exception $e) {
            return $this->safeErrorResponse($e, __('Failed to retrieve bot.'));
        }
    }

    /**
     * Update the specified bot.
     * @throws Throwable
     */
    public function update(BotRequest $request, string $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $user = auth()->user();
            $bot = Bot::where('uuid', $id)->firstOrFail();

            // Check if user can edit this bot
            if (!$bot->userCan($user->id, 'edit')) {
                return $this->errorResponse(__('Access denied.'), 403);
            }

            $data = $request->except(['knowledge_sources']);
            $data['model_ai_id'] = ModelAIFacade::getModelByKey($request->input('model'))?->id;
            $data['is_shareable'] = $request->input('is_shareable') == 'true' ? 1 : 0;
            $metadata = $bot->metadata ?? [];
            $knowledgeData = $request->input('knowledge', []);
            $currentRagConfig = $metadata['rag_config'] ?? [];

            $metadata['rag_config'] = [
                'enabled' => $knowledgeData['enabled'] ?? $currentRagConfig['enabled'] ?? false,
                'auto_query' => $knowledgeData['auto_query'] ?? $currentRagConfig['auto_query'] ?? false,
                'top_k' => $knowledgeData['top_k'] ?? $currentRagConfig['top_k'] ?? 5,
                'collection' => $currentRagConfig['collection'] ?? 'documents'
            ];

            $data['metadata'] = $metadata;

            if ($request->has('logo')) {
                $data['logo'] = $this->moveLogoFromTemp($request->input('logo'), $bot->uuid);
            }

            $bot->update($data);

            $pendingKnowledgeBaseIds = $this->processKnowledgeSources($request, $bot);

            // Dispatch RAG processing job if there are pending files
            if (!empty($pendingKnowledgeBaseIds)) {
                RAGFileProcessingJob::dispatch(
                    $pendingKnowledgeBaseIds,
                    $user->id,
                    get_class($user),
                    'bot_update',
                    $bot->id
                );
            }

            $bot->load(['aiModel:id,key,name', 'owner:id,first_name,last_name,full_name,avatar']);

            DB::commit();

            return $this->successResponse($bot, __('Bot updated successfully.'));

        } catch (Exception $e) {
            DB::rollBack();
            return $this->safeErrorResponse($e);
        }
    }

    /**
     * Soft delete the specified bot.
     */
    public function delete(string $id): JsonResponse
    {
        try {
            $user = auth()->user();
            $bot = Bot::where('uuid', $id)->firstOrFail();

            // Check if user can delete this bot
            if (!$bot->userCan($user->id, 'delete')) {
                return $this->errorResponse(__('Access denied.'), 403);
            }

            $bot->delete();

            return $this->successResponse(null, __('Bot deleted successfully.'));

        } catch (Exception $e) {
            return $this->safeErrorResponse($e);
        }
    }

    /**
     * Permanently delete the specified bot.
     */
    public function destroy(string $id): JsonResponse
    {
        try {
            $user = auth()->user();
            $bot = Bot::withTrashed()->where('uuid', $id)->firstOrFail();

            // Check if user can destroy this bot
            if (!$bot->userCan($user->id, 'destroy')) {
                return $this->errorResponse(__('Access denied.'), 403);
            }

            $bot->forceDelete();

            return $this->successResponse(null, __('Bot permanently deleted.'));

        } catch (Exception $e) {
            return $this->safeErrorResponse($e, __('Failed to permanently delete bot.'));
        }
    }

    /**
     * Restore the specified bot.
     */
    public function restore(string $id): JsonResponse
    {
        try {
            $user = auth()->user();
            $bot = Bot::withTrashed()->where('uuid', $id)->firstOrFail();
            // Check if user can restore this bot
            if (!$bot->userCan($user->id, 'delete')) {
                return $this->errorResponse(__('Access denied.'), 403);
            }

            $bot->restore();

            return $this->successResponse(null, __('Bot restored successfully.'));

        } catch (Exception $e) {
            return $this->safeErrorResponse($e, __('Failed to restore bot.'));
        }
    }

    /**
     * Bulk soft delete bots.
     */
    public function bulkDelete(Request $request): JsonResponse
    {
        $request->validate([
            'ids' => 'required|array|min:1',
            'ids.*' => 'required|string|exists:bots,uuid'
        ]);

        $user = auth()->user();
        $deletedCount = 0;
        $errors = [];

        foreach ($request->ids as $id) {
            try {
                $bot = Bot::where('uuid', $id)->first();
                if ($bot && $bot->userCan($user->id, 'delete')) {
                    $bot->delete();
                    $deletedCount++;
                } else {
                    $errors[] = "Access denied for bot: {$id}";
                }
            } catch (Exception $e) {
                $errors[] = "Failed to delete bot {$id}: " . $e->getMessage();
            }
        }

        $message = $deletedCount > 0
            ? __('Successfully deleted :count bots.', ['count' => $deletedCount])
            : __('No bots were deleted.');

        return $this->successResponse(['deleted_count' => $deletedCount, 'errors' => $errors], $message);
    }

    /**
     * Bulk restore bots.
     */
    public function bulkRestore(Request $request): JsonResponse
    {
        $request->validate([
            'ids' => 'required|array|min:1',
            'ids.*' => 'required|string|exists:bots,uuid'
        ]);

        $user = auth()->user();
        $restoredCount = 0;
        $errors = [];

        foreach ($request->ids as $id) {
            try {
                $bot = Bot::withTrashed()->where('uuid', $id)->first();
                if ($bot && $bot->userCan($user->id, 'delete')) {
                    $bot->restore();
                    $restoredCount++;
                } else {
                    $errors[] = "Access denied for bot: {$id}";
                }
            } catch (Exception $e) {
                $errors[] = "Failed to restore bot {$id}: " . $e->getMessage();
            }
        }

        $message = $restoredCount > 0
            ? __('Successfully restored :count bots.', ['count' => $restoredCount])
            : __('No bots were restored.');

        return $this->successResponse(['restored_count' => $restoredCount, 'errors' => $errors], $message);
    }

    /**
     * Bulk permanently delete bots.
     */
    public function bulkDestroy(Request $request): JsonResponse
    {
        $request->validate([
            'ids' => 'required|array|min:1',
            'ids.*' => 'required|string|exists:bots,uuid'
        ]);

        $user = auth()->user();
        $destroyedCount = 0;
        $errors = [];

        foreach ($request->ids as $id) {
            try {
                $bot = Bot::withTrashed()->where('uuid', $id)->first();
                if ($bot && $bot->userCan($user->id, 'destroy')) {
                    $bot->forceDelete();
                    $destroyedCount++;
                } else {
                    $errors[] = "Access denied for bot: {$id}";
                }
            } catch (Exception $e) {
                $errors[] = "Failed to destroy bot {$id}: " . $e->getMessage();
            }
        }

        $message = $destroyedCount > 0
            ? __('Successfully destroyed :count bots.', ['count' => $destroyedCount])
            : __('No bots were destroyed.');

        return $this->successResponse(['destroyed_count' => $destroyedCount, 'errors' => $errors], $message);
    }

    /**
     * Get bots for dropdown.
     */
    public function dropdown(): JsonResponse
    {
        $user = auth()->user();

        $bots = Bot::query()
            ->select(['uuid', 'name'])
            ->where([
                'owner_id' => $user->id,
                'owner_type' => get_class($user),
                'status' => 'active'
            ])
            ->orderBy('name')
            ->get();

        return $this->successResponse($bots, __('Bots retrieved successfully.'));
    }

    /**
     * Handle logo file upload to temporary directory.
     */
    private function handleLogoUpload(Request $request): string
    {
        $request->validate([
            'logo' => 'required|file|image|mimes:jpeg,jpg,png,gif,svg,webp|max:10240' // 10MB
        ]);

        $file = $request->file('logo');
        // Generate unique filename
        $filename = time() . '_' . Str::uuid() . '.' . $file->getClientOriginalExtension();

        // Store in temporary directory
        $logoPath = $file->storeAs("logos/temp", $filename, 'public');

        return $this->successResponse(
            [
                'logo' => $logoPath,
                'logoUrl' => url(Storage::url($logoPath))
            ],
            __('Organization logo uploaded successfully.')
        );
    }

    /**
     * Update RAG configuration for a specific bot.
     */
    public function updateRagConfig(Request $request, string $id): JsonResponse
    {
        $request->validate([
            'enabled' => 'required|boolean',
            'auto_query' => 'boolean',
            'top_k' => 'integer|min:1|max:20',
            'collection' => 'string|max:100',
            'min_question_length' => 'integer|min:1|max:500',
        ]);

        try {
            $user = auth()->user();
            $bot = Bot::where('uuid', $id)->firstOrFail();

            // Check ownership
            if (!$this->canUserAccessBot($user, $bot)) {
                return $this->errorResponse('Access denied', 403);
            }

            // Update RAG config in metadata
            $metadata = $bot->metadata ?? [];
            $metadata['rag_config'] = [
                'enabled' => $request->input('enabled'),
                'auto_query' => $request->input('auto_query', false),
                'top_k' => $request->input('top_k', 5),
                'collection' => $request->input('collection', 'documents'),
                'min_question_length' => $request->input('min_question_length', 10),
            ];

            $bot->update(['metadata' => $metadata]);

            return $this->successResponse(
                [
                    'bot_id' => $bot->uuid,
                    'rag_config' => $metadata['rag_config']
                ],
                'RAG configuration updated successfully'
            );

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Bot not found', 404);
        } catch (Exception $e) {
            return $this->safeErrorResponse($e, 500);
        }
    }

    /**
     * Get RAG configuration for a specific bot.
     */
    public function getRagConfig(string $id): JsonResponse
    {
        try {
            $user = auth()->user();
            $bot = Bot::where('uuid', $id)->firstOrFail();

            // Check ownership
            if (!$this->canUserAccessBot($user, $bot)) {
                return $this->errorResponse('Access denied', 403);
            }

            $ragConfig = $bot->metadata['rag_config'] ?? [
                'enabled' => false,
                'auto_query' => false,
                'top_k' => 5,
                'collection' => 'documents',
                'min_question_length' => 10,
            ];

            return $this->successResponse(
                [
                    'bot_id' => $bot->uuid,
                    'rag_config' => $ragConfig
                ],
                'RAG configuration retrieved successfully'
            );

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Bot not found', 404);
        } catch (Exception $e) {
            return $this->safeErrorResponse($e, 500);
        }
    }

    /**
     * Process knowledge sources for bot creation/update.
     */
    private function processKnowledgeSources(BotRequest $request, Bot $bot): array
    {
        $pendingKnowledgeBaseIds = [];
        $knowledgeSources = $request->input('knowledge', []);

        if (empty($knowledgeSources)) {
            return $pendingKnowledgeBaseIds;
        }

        // Clear existing knowledge base associations
        $bot->knowledgeBases()->detach();

        // Process new file uploads
        $pendingKnowledgeBaseIds = array_merge(
            $pendingKnowledgeBaseIds,
            $this->processNewUploads($knowledgeSources, $bot)
        );

        // Process text inputs
        $pendingKnowledgeBaseIds = array_merge(
            $pendingKnowledgeBaseIds,
            $this->processTextInputs($knowledgeSources, $bot)
        );

        // Process existing library files
        return array_merge(
            $pendingKnowledgeBaseIds,
            $this->processLibraryFiles($knowledgeSources, $bot)
        );
    }

    /**
     * Process new file uploads.
     */
    private function processNewUploads(array $knowledgeSources, Bot $bot): array
    {
        $pendingIds = [];

        if (!isset($knowledgeSources['new_uploads']) || !is_array($knowledgeSources['new_uploads'])) {
            return $pendingIds;
        }

        foreach ($knowledgeSources['new_uploads'] as $file) {
            try {
                $tempPath = $file['storage_path'] ?? null;
                if (!$tempPath || !Storage::disk('local')->exists($tempPath)) {
                    continue;
                }

                $permanentPath = Str::replace('/temp', '', $tempPath);
                Storage::disk('local')->move($tempPath, $permanentPath);

                $knowledgeBase = KnowledgeBase::createKnowledgeBase([
                    'type' => 'file',
                    'storage_path' => $permanentPath,
                    'name' => $file['name'] ?? 'Untitled'
                ]);

                $bot->knowledgeBases()->attach($knowledgeBase->id);
                $pendingIds[] = $knowledgeBase->id;
            } catch (Exception $e) {
                Log::error('Failed to process file upload', [
                    'file' => $file,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $pendingIds;
    }

    /**
     * Process text inputs.
     */
    private function processTextInputs(array $knowledgeSources, Bot $bot): array
    {
        $pendingIds = [];
        $textInput = $knowledgeSources['text'] ?? null;

        if (!is_string($textInput) || empty(trim($textInput))) {
            return $pendingIds;
        }

        try {
            $knowledgeBase = KnowledgeBase::createKnowledgeBase([
                'type' => 'text',
                'storage_path' => null,
                'name' => 'Text Input - ' . Str::limit($textInput, 20)
            ]);

            $knowledgeBase->generateTextFilePath($textInput);
            $bot->knowledgeBases()->attach($knowledgeBase->id);
            $pendingIds[] = $knowledgeBase->id;

        } catch (Exception $e) {
            Log::error('Failed to process text input', [
                'text_preview' => Str::limit($textInput, 100),
                'error' => $e->getMessage()
            ]);
        }

        return $pendingIds;
    }

    /**
     * Process existing library files.
     */
    private function processLibraryFiles(array $knowledgeSources, Bot $bot): array
    {
        $pendingIds = [];

        if (!isset($knowledgeSources['library_files']) || !is_array($knowledgeSources['library_files'])) {
            return $pendingIds;
        }

        $user = auth()->user();
        $existingKnowledgeBases = KnowledgeBase::whereIn('uuid', $knowledgeSources['library_files'])
            ->where('owner_id', $user->id)
            ->where('owner_type', get_class($user))
            ->get();

        foreach ($existingKnowledgeBases as $kb) {
            $bot->knowledgeBases()->attach($kb->id);

            if (!$kb->isReady()) {
                $pendingIds[] = $kb->id;
            }
        }

        return $pendingIds;
    }

    /**
     * Move logo from temp to organization directory.
     */
    private function moveLogoFromTemp(string $tempPath, string $uuid): ?string
    {
        if (!$tempPath || !Storage::disk('public')->exists($tempPath)) {
            return null;
        }

        if (!Str::contains($tempPath, '/temp/')) {
            return $tempPath;
        }

        $filename = basename($tempPath);

        $newPath = "logos/bots/{$uuid}/{$filename}";

        // Move file from temp to organization directory
        if (Storage::disk('public')->move($tempPath, $newPath)) {
            return $newPath;
        }

        return null;
    }


}
