<?php

namespace Modules\Organization\Http\Requests\Bot;

use Modules\Core\Http\Requests\BaseFormRequest;
use Modules\Organization\Traits\OrganizationAuthorization;

class BulkBotRequest extends BaseFormRequest
{
    use OrganizationAuthorization;

    public function rules(): array
    {
        return [
            'ids' => 'required|array|min:1',
            'ids.*' => 'required|string|exists:users,uuid',
            'status' => 'nullable|string|in:active,inactive'
        ];
    }

    public function authorize(): bool
    {
        return $this->hasOrganizationAdminAccess();
    }
}
