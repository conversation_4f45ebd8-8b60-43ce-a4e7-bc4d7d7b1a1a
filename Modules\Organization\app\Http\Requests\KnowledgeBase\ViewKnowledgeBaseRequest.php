<?php

namespace Modules\Organization\Http\Requests\KnowledgeBase;

use Modules\Core\Http\Requests\BaseFormRequest;
use Modules\Organization\Traits\OrganizationAuthorization;

class ViewKnowledgeBaseRequest extends BaseFormRequest
{
    use OrganizationAuthorization;

    public function rules(): array
    {
        return [
            'limit' => 'nullable|integer|min:1|max:100',
            'page' => 'nullable|integer|min:1',
            'search' => 'nullable|string|max:255',
            'status' => 'nullable|string|in:active,inactive,processing,failed',
            'type' => 'nullable|string|in:text,file,url',
            'created_from' => 'nullable|date',
            'created_to' => 'nullable|date|after_or_equal:created_from'
        ];
    }

    public function authorize(): bool
    {
        return $this->hasOrganizationAccess();
    }
}
