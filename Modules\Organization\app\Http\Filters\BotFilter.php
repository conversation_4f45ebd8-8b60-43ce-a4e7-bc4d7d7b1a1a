<?php

namespace Modules\Organization\Http\Filters;

use Modules\Core\Abstracts\AbstractFilter;

class BotFilter extends AbstractFilter
{
    /**
     * Define the filters available for this model.
     */
    protected function filters(): array
    {
        return [
            'name' => 'like',
            'status' => 'exact',
            'visibility' => 'exact',
            'created_from' => ['type' => 'from', 'column' => 'created_at'],
            'created_to' => ['type' => 'to', 'column' => 'created_at'],
            'search' => 'custom',
            'model' => 'custom'
        ];
    }

    /**
     * Apply custom filters
     */
    protected function applyCustomFilter($query, string $field, mixed $value): void
    {
        if ($field === 'search') {
            $query->where(function ($q) use ($value) {
                $q->where('name', 'like', "%{$value}%")
                  ->orWhere('description', 'like', "%{$value}%");
            });
        } elseif ($field === 'model') {
            $query->whereHas('aiModel', function ($q) use ($value) {
                $q->where('key', $value);
            });
        }
    }
}
