<?php

use Illuminate\Support\Facades\Route;
use Modules\Organization\Http\Controllers\Auth\OrganizationController as AuthOrganizationController;
use Modules\Organization\Http\Controllers\Auth\MemberController;
use Modules\Organization\Http\Controllers\Auth\InvitationController;
use Modules\Organization\Http\Controllers\Auth\BotController;
use Modules\Organization\Http\Controllers\Auth\KnowledgeBaseController;
use Modules\Organization\Http\Controllers\Auth\GuestController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Authenticated API routes (admin/auth)
Route::middleware(['auth:api'])->prefix('v1/auth')->group(function () {

    // Organization Access Check (requires auth)
    Route::get('organizations/{orgUuid}/access', [AuthOrganizationController::class, 'checkAccess'])->name('auth.organization.access');

    // Organization Management
    Route::prefix('organizations')->name('auth.organizations.')->group(function () {
        // Organization utility routes
        Route::get('dropdown', [AuthOrganizationController::class, 'dropdown'])->name('dropdown');
        Route::post('upload-logo', [AuthOrganizationController::class, 'uploadLogo'])->name('upload-logo');
        Route::get('{id}/overview', [AuthOrganizationController::class, 'overview'])->name('overview');

        // Organization bulk operations
        Route::delete('bulk/delete', [AuthOrganizationController::class, 'bulkDelete'])->name('bulk-delete');
        Route::delete('bulk/force', [AuthOrganizationController::class, 'bulkDestroy'])->name('bulk-destroy');
        Route::put('bulk/restore', [AuthOrganizationController::class, 'bulkRestore'])->name('bulk-restore');

        // Standard CRUD routes
        Route::get('/', [AuthOrganizationController::class, 'index'])->name('index');
        Route::post('/', [AuthOrganizationController::class, 'store'])->name('store');
        Route::get('{id}', [AuthOrganizationController::class, 'show'])->name('show');
        Route::put('{id}', [AuthOrganizationController::class, 'update'])->name('update');
        Route::patch('{id}', [AuthOrganizationController::class, 'update'])->name('update');

        // Organization delete operations
        Route::delete('{id}/delete', [AuthOrganizationController::class, 'delete'])->name('delete');
        Route::delete('{id}/force', [AuthOrganizationController::class, 'destroy'])->name('destroy');
        Route::put('{id}/restore', [AuthOrganizationController::class, 'restore'])->name('restore');
    });



    // Organization Member Management
    Route::prefix('organizations/{orgUuid}/members')->name('auth.organization.members.')->group(function () {
        // Member utility routes
        Route::get('dropdown', [MemberController::class, 'dropdown'])->name('dropdown');

        // Member bulk operations
        Route::delete('bulk/force', [MemberController::class, 'bulkDestroy'])->name('bulk-destroy');
        Route::patch('bulk/update-role', [MemberController::class, 'bulkUpdateRole'])->name('bulk-update-role');

        // Standard CRUD routes
        Route::get('/', [MemberController::class, 'index'])->name('index');
        Route::get('/find', [MemberController::class, 'find'])->name('find');
        Route::post('/', [MemberController::class, 'store'])->name('store');
        Route::get('{uuid}', [MemberController::class, 'show'])->name('show');
        Route::put('{uuid}', [MemberController::class, 'update'])->name('update');
        Route::patch('{uuid}', [MemberController::class, 'update'])->name('update');

        // Member delete operations
        Route::delete('{uuid}/force', [MemberController::class, 'destroy'])->name('destroy');
    });

    // Organization Guest Management
    Route::prefix('organizations/{orgUuid}/guests')->name('auth.organization.guests.')->group(function () {
        // Guest utility routes
        Route::get('dropdown', [GuestController::class, 'dropdown'])->name('dropdown');

        // Guest bulk operations
        Route::delete('bulk/force', [GuestController::class, 'bulkDestroy'])->name('bulk-destroy');

        // Standard CRUD routes
        Route::get('/', [GuestController::class, 'index'])->name('index');
        Route::get('/find', [GuestController::class, 'find'])->name('find');
        Route::post('/', [GuestController::class, 'store'])->name('store');
        Route::get('{uuid}', [GuestController::class, 'show'])->name('show');
        Route::put('{uuid}', [GuestController::class, 'update'])->name('update');
        Route::patch('{uuid}', [GuestController::class, 'update'])->name('update');

        // Guest delete operations
        Route::delete('{uuid}/force', [GuestController::class, 'delete'])->name('delete');
    });

    // Organization Invitation Management
    Route::prefix('organizations/{orgUuid}/invitations')->name('auth.organization.invitations.')->group(function () {
        // Invitation utility routes

        // Invitation bulk operations (MUST be before parameterized routes)
        Route::post('bulk/cancel', [InvitationController::class, 'bulkCancel'])->name('bulk-cancel');
        Route::post('bulk/resend', [InvitationController::class, 'bulkResend'])->name('bulk-resend');

        // Standard CRUD routes
        Route::get('/', [InvitationController::class, 'index'])->name('index');
        Route::post('/', [InvitationController::class, 'store'])->name('store');
        Route::get('{id}', [InvitationController::class, 'show'])->name('show');
        Route::put('{id}', [InvitationController::class, 'update'])->name('update');

        // Invitation specific operations
        Route::post('{id}/resend', [InvitationController::class, 'resend'])->name('resend');
        Route::post('{id}/cancel', [InvitationController::class, 'cancel'])->name('cancel');

        // Invitation delete operations
        Route::delete('{id}/delete', [InvitationController::class, 'delete'])->name('delete');
    });

    // Organization Bot Management
    Route::prefix('organizations/{orgUuid}/bots')->name('auth.organization.bots.')->group(function () {
        // Bot utility routes
        Route::get('dropdown', [BotController::class, 'dropdown'])->name('dropdown');
        Route::post('upload-avatar', [BotController::class, 'handleLogoUpload'])->name('upload-avatar');

        // Bot bulk operations
        Route::delete('bulk/delete', [BotController::class, 'bulkDelete'])->name('bulk-delete');
        Route::delete('bulk/force', [BotController::class, 'bulkDestroy'])->name('bulk-destroy');
        Route::put('bulk/restore', [BotController::class, 'bulkRestore'])->name('bulk-restore');
        Route::post('bulk/duplicate', [BotController::class, 'bulkDuplicate'])->name('bulk-duplicate');
        Route::patch('bulk/toggle-status', [BotController::class, 'bulkToggleStatus'])->name('bulk-toggle-status');

        // Standard CRUD routes
        Route::get('/', [BotController::class, 'index'])->name('index');
        Route::post('/', [BotController::class, 'store'])->name('store');
        Route::get('{uuid}', [BotController::class, 'show'])->name('show');
        Route::put('{uuid}', [BotController::class, 'update'])->name('update');
        Route::patch('{uuid}', [BotController::class, 'update'])->name('update');

        // Bot delete operations
        Route::delete('{uuid}/delete', [BotController::class, 'delete'])->name('delete');
        Route::delete('{uuid}/force', [BotController::class, 'destroy'])->name('destroy');
        Route::put('{uuid}/restore', [BotController::class, 'restore'])->name('restore');

        // Bot specific operations
        Route::post('{uuid}/duplicate', [BotController::class, 'duplicate'])->name('duplicate');
        Route::post('{uuid}/toggle-status', [BotController::class, 'toggleStatus'])->name('toggle-status');

        // Bot user assignment (for private bots)
        Route::post('{uuid}/assign-users', [BotController::class, 'assignUsers'])->name('assign-users');
        Route::delete('{uuid}/unassign-users', [BotController::class, 'unassignUsers'])->name('unassign-users');
        Route::get('{uuid}/users', [BotController::class, 'getAssignedUsers'])->name('users');
    });

    // Organization Knowledge Base Management
    Route::prefix('organizations/{orgUuid}/knowledge-bases')->name('auth.organization.knowledge-bases.')->group(function () {
        // Knowledge base utility routes
        Route::get('dropdown', [KnowledgeBaseController::class, 'dropdown'])->name('dropdown');
        Route::get('files', [KnowledgeBaseController::class, 'getFiles'])->name('files');

        // Knowledge base bulk operations
        Route::delete('bulk/delete', [KnowledgeBaseController::class, 'bulkDelete'])->name('bulk-delete');
        Route::delete('bulk/force', [KnowledgeBaseController::class, 'bulkDestroy'])->name('bulk-destroy');
        Route::put('bulk/restore', [KnowledgeBaseController::class, 'bulkRestore'])->name('bulk-restore');
        Route::post('bulk/upload', [KnowledgeBaseController::class, 'bulkUpload'])->name('bulk-upload');
        Route::post('bulk/retrain', [KnowledgeBaseController::class, 'bulkRetrain'])->name('bulk-retrain');

        // Knowledge base creation routes
        Route::post('text', [KnowledgeBaseController::class, 'createFromText'])->name('create-text');
        Route::post('file', [KnowledgeBaseController::class, 'createFromFile'])->name('create-file');

        // File upload management
        Route::post('files/upload', [KnowledgeBaseController::class, 'storeFileUpload'])->name('files.upload');
        Route::delete('files/remove', [KnowledgeBaseController::class, 'removeFileUpload'])->name('files.remove');

        // Standard CRUD routes
        Route::get('/', [KnowledgeBaseController::class, 'index'])->name('index');
        Route::post('/', [KnowledgeBaseController::class, 'store'])->name('store');
        Route::get('{uuid}', [KnowledgeBaseController::class, 'show'])->name('show');
        Route::put('{uuid}', [KnowledgeBaseController::class, 'update'])->name('update');
        Route::patch('{uuid}', [KnowledgeBaseController::class, 'update'])->name('update');

        // Knowledge base delete operations
        Route::delete('{uuid}/delete', [KnowledgeBaseController::class, 'delete'])->name('delete');
        Route::delete('{uuid}/force', [KnowledgeBaseController::class, 'destroy'])->name('destroy');
        Route::put('{uuid}/restore', [KnowledgeBaseController::class, 'restore'])->name('restore');

        // Knowledge base specific operations
        Route::post('{uuid}/retrain', [KnowledgeBaseController::class, 'retrain'])->name('retrain');
    });

    // Organization Settings Management
    Route::prefix('organizations/{orgUuid}/settings')->name('auth.organization.settings.')->group(function () {
        Route::get('/', [\Modules\Organization\Http\Controllers\Auth\OrganizationSettingsController::class, 'index'])->name('index');
        Route::put('/', [\Modules\Organization\Http\Controllers\Auth\OrganizationSettingsController::class, 'update'])->name('update');
        Route::delete('/reset', [\Modules\Organization\Http\Controllers\Auth\OrganizationSettingsController::class, 'reset'])->name('reset');
    });
});
