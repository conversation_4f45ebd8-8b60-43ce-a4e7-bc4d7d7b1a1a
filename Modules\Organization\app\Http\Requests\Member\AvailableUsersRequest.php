<?php

namespace Modules\Organization\Http\Requests\Member;

use Modules\Core\Http\Requests\BaseFormRequest;

class AvailableUsersRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'limit' => 'nullable|integer|min:1|max:100',
            'page' => 'nullable|integer|min:1',
            'search' => 'nullable|string|max:255',
            'email' => 'nullable|string|email|max:255',
            'name' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:20',
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Only authenticated users with admin or super-admin roles can access this endpoint
        $user = auth()->user();

        if (!$user) {
            return false;
        }

        // Super-admin can always access
        if ($user->hasRole('super-admin')) {
            return true;
        }

        // Admin users can access (they need to be able to invite users to organizations)
        return $user->hasRole('admin');
    }
}
