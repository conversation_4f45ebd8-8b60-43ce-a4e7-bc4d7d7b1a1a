<?php

namespace Modules\Organization\Traits;

use Illuminate\Support\Facades\Cache;
use Modules\Organization\Models\Organization;

trait OrganizationAuthorization
{
    /**
     * Get organization from route parameter
     */
    protected function getOrganization(): Organization
    {
        $orgUuid = request()->route('orgUuid');

        return Cache::tags(['organizations'])->remember(
            key: "organization.{$orgUuid}",
            ttl: 300,
            callback: fn() => Organization::where('uuid', $orgUuid)->firstOrFail()
        );
    }

    /**
     * Check if user has organization access (member level) with cache
     */
    protected function hasOrganizationAccess(): bool
    {
        $user = $this->user();
        $organization = $this->getOrganization(); // Sử dụng cache

        // 1. Super-admin bypass
        if ($user->hasRole(['super-admin'], 'api')) {
            return true;
        }

        // 2. Owner check
        if ($organization->owner_id === $user->id) {
            return true;
        }

        // 3. Member check
        return $organization->hasMember($user);
    }

    /**
     * Check if user has organization admin access with cache
     */
    protected function hasOrganizationAdminAccess(): bool
    {
        $user = $this->user();
        $organization = $this->getOrganization(); // Sử dụng cache

        // 1. Super-admin bypass
        if ($user->hasRole(['super-admin'], 'api')) {
            return true;
        }

        // 2. Owner check
        if ($organization->owner_id === $user->id) {
            return true;
        }

        // 3. Admin role check
        return $organization->getMemberRole($user) === 'admin';
    }

    /**
     * Check if user has owner access to organization (owner only).
     */
    protected function hasOrganizationOwnerAccess(): bool
    {
        $organization = $this->getOrganization();
        $user = auth()->user();

        if (!$user || !$organization) {
            return false;
        }

        // Super admin can access everything
        if ($user->hasRole('super-admin')) {
            return true;
        }

        // Only owner has owner access
        return $organization->owner_id === $user->id;
    }
}
