<?php

namespace Modules\Organization\Http\Requests\KnowledgeBase;

use Modules\Core\Http\Requests\BaseFormRequest;
use Modules\Organization\Traits\OrganizationAuthorization;

class BulkKnowledgeBaseRequest extends BaseFormRequest
{
    use OrganizationAuthorization;

    public function rules(): array
    {
        return [
            'ids' => 'required|array|min:1',
            'ids.*' => 'required|string|exists:knowledge_bases,uuid',
            'status' => 'nullable|string|in:active,inactive'
        ];
    }

    public function authorize(): bool
    {
        return $this->hasOrganizationAdminAccess();
    }
}
