<?php

namespace Modules\Organization\Http\Requests\Organization;

use Modules\Core\Http\Requests\BaseFormRequest;
use Modules\Organization\Models\Organization;

class LogoUploadRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'logo' => [
                'required',
                'file',
                'image',
                'mimes:jpeg,jpg,png,gif,svg,webp',
                'max:10240', // 10MB
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'logo.required' => __('Logo file is required.'),
            'logo.file' => __('Logo must be a valid file.'),
            'logo.image' => __('Logo must be an image file.'),
            'logo.mimes' => __('Logo must be a file of type: jpeg, jpg, png, gif, svg, webp.'),
            'logo.max' => __('Logo file size cannot exceed 10MB.'),
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $user = $this->user();

        if (!$user) {
            return false;
        }

        // Allow super-admin to upload logos to temp storage
        if ($user->hasRole('super-admin', 'api')) {
            return true;
        }

        // Allow users with organization.edit permission to upload logos
        if ($user->hasPermissionTo('organization.edit', 'api')) {
            return true;
        }

        // Check if user is owner or admin of any organization
        // This allows organization owners and admins to upload logos to temp storage
        $isOrganizationOwnerOrAdmin = Organization::where(function ($query) use ($user) {
            $query->where('owner_id', $user->id)
                  ->orWhereHas('organizationMembers', function ($subQuery) use ($user) {
                      $subQuery->where('user_id', $user->id)
                               ->where('role', 'admin');
                  });
        })->exists();

        return $isOrganizationOwnerOrAdmin;
    }
}
