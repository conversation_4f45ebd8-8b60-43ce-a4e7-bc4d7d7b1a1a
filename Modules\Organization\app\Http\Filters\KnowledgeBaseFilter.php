<?php

namespace Modules\Organization\Http\Filters;

use Modules\Core\Abstracts\AbstractFilter;

class KnowledgeBaseFilter extends AbstractFilter
{
    /**
     * Define the filters available for this model.
     */
    protected function filters(): array
    {
        return [
            'name' => 'like',
            'type' => 'exact',
            'status' => 'exact',
            'is_trashed' => 'trashed'
        ];
    }

    /**
     * Apply custom search filter for name and description
     */
    protected function applyCustomFilter($query, string $field, mixed $value): void
    {
        if ($field === 'search') {
            $query->where(function ($q) use ($value) {
                $q->where('name', 'like', "%{$value}%")
                  ->orWhere('content', 'like', "%{$value}%");
            });
        }
    }
}
