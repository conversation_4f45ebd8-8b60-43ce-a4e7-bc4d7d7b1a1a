<?php

namespace Modules\Organization\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Modules\Organization\Http\Filters\BotFilter;
use Modules\ChatBot\Models\Bot;
use Modules\ChatBot\Models\BotStatus;
use Modules\ChatBot\Enums\BotVisibility;
use Modules\Core\Traits\ResponseTrait;
use Modules\ModelAI\Facades\ModelAIFacade;
use Modules\Organization\Http\Requests\Bot\BotRequest;
use Modules\Organization\Http\Requests\Bot\BulkBotRequest;
use Modules\Organization\Http\Requests\Bot\DeleteBotRequest;
use Modules\Organization\Http\Requests\Bot\LogoUploadRequest;
use Modules\Organization\Http\Requests\Bot\ViewBotRequest;
use Modules\Organization\Models\BotUser;
use Modules\Organization\Models\Organization;
use Modules\Organization\Traits\OrganizationAuthorization;
use Modules\User\Models\User;
use Illuminate\Http\Request;

class BotController extends Controller
{
    use ResponseTrait, OrganizationAuthorization;

    /**
     * No middleware - using FormRequest + Policy for authorization
     */
    public function __construct()
    {
        // Authorization handled in FormRequest and Policy
    }

    public function index(ViewBotRequest $request, string $orgUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $user = $request->user();
        $query = $organization->bots()->with(['aiModel:id,key,name', 'owner:id,name']);

        $userRole = $organization->getMemberRole($user);
        if (!$user->hasRole('super-admin') && !$organization->isOwner($user) && $userRole !== 'admin') {
            $query->where(fn($q) => $q->where('visibility', 'public')->orWhereRelation('botUsers', 'user_id', $user->id));
        }

        $bots = $query->filter(new BotFilter($request))
                      ->paginate($request->input('limit', 10))
                      ->through(function ($bot) use ($user, $userRole) {
                            if ($userRole === 'admin' || $bot->owner_id === $user->id) {
                             $bot->makeVisible(['system_prompt']);
                          }
                          return $bot;
                      });

        return $this->paginatedResponse($bots, 'Organization bots retrieved successfully.');
    }

    public function store(BotRequest $request, string $orgUuid): JsonResponse
    {
        $organization = $this->getOrganization();

        $bot = DB::transaction(function () use ($request, $organization) {
            $data = $this->prepareBotData($request, $organization->id);
            $bot = Bot::create($data);
            $this->processKnowledgeSources($request, $bot, $organization);
            return $bot;
        });

        $bot->load(['aiModel:id,key,name', 'owner:id,name']);
        return $this->successResponse($bot, 'Organization bot created successfully.', 201);
    }

    public function show(ViewBotRequest $request, string $orgUuid, string $botUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $bot = $this->findBot($organization, $botUuid);

        $bot->load(['aiModel:id,key,name', 'owner:id,name', 'knowledgeBases']);
        return $this->successResponse($bot, 'Organization bot retrieved successfully.');
    }

    public function update(BotRequest $request, string $orgUuid, string $botUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $bot = $this->findBot($organization, $botUuid);

        DB::transaction(function () use ($request, $bot, $organization) {
            $data = $request->except(['knowledge_sources']);
            if ($request->has('model')) {
                $data['model_ai_id'] = ModelAIFacade::getModelByKey($request->input('model'))?->id;
            }
            $bot->update($data);
            $this->processKnowledgeSources($request, $bot, $organization);
        });

        $bot->load(['aiModel:id,key,name', 'owner:id,name', 'knowledgeBases']);
        return $this->successResponse($bot, 'Organization bot updated successfully.');
    }

    public function delete(DeleteBotRequest $request, string $orgUuid, string $botUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $bot = $this->findBot($organization, $botUuid);

        $bot->delete();
        return $this->successResponse(null, 'Organization bot deleted successfully.');
    }

    public function destroy(DeleteBotRequest $request, string $orgUuid, string $botUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $bot = $this->findBot($organization, $botUuid, true);

        $bot->forceDelete();
        return $this->successResponse(null, 'Organization bot permanently deleted successfully.');
    }

    public function restore(DeleteBotRequest $request, string $orgUuid, string $botUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $bot = $this->findBot($organization, $botUuid, true);

        $bot->restore();
        $bot->load(['aiModel:id,key,name', 'owner:id,name']);
        return $this->successResponse($bot, 'Organization bot restored successfully.');
    }

    public function dropdown(ViewBotRequest $request, string $orgUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $bots = $organization->bots()
            ->where('status', 'active')
            ->select('uuid', 'name')
            ->get();

        return $this->successResponse($bots, 'Organization bots dropdown retrieved successfully.');
    }

    public function duplicate(BotRequest $request, string $orgUuid, string $botUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $originalBot = $this->findBot($organization, $botUuid);

        $duplicateBot = DB::transaction(function () use ($originalBot) {
            $duplicate = $originalBot->replicate();
            $duplicate->name = $originalBot->name . ' (Copy)';
            $duplicate->api_key = 'pk_' . Str::random(48);
            $duplicate->save();

            $knowledgeBaseIds = $originalBot->knowledgeBases()->pluck('id');
            if ($knowledgeBaseIds->isNotEmpty()) {
                $duplicate->knowledgeBases()->sync($knowledgeBaseIds);
            }
            return $duplicate;
        });

        $duplicateBot->load(['aiModel:id,key,name', 'owner:id,name']);
        return $this->successResponse($duplicateBot, 'Organization bot duplicated successfully.', 201);
    }

    public function toggleStatus(BotRequest $request, string $orgUuid, string $botUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $bot = $this->findBot($organization, $botUuid);

        $newStatus = $bot->status === BotStatus::ACTIVE ? BotStatus::INACTIVE : BotStatus::ACTIVE;
        $bot->update(['status' => $newStatus]);
        $bot->load(['aiModel:id,key,name', 'owner:id,name']);
        return $this->successResponse($bot, "Organization bot status changed to {$newStatus->value} successfully.");
    }

    public function bulkDelete(BulkBotRequest $request, string $orgUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $deletedCount = $organization->bots()->whereIn('uuid', $request->input('ids'))->delete();
        return $this->successResponse(['deleted_count' => $deletedCount], "Successfully deleted {$deletedCount} organization bots.");
    }

    public function bulkDestroy(BulkBotRequest $request, string $orgUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $bots = $organization->bots()->withTrashed()->whereIn('uuid', $request->input('ids'))->get();
        $destroyedCount = 0;
        foreach ($bots as $bot) {
            if ($bot->forceDelete()) $destroyedCount++;
        }
        return $this->successResponse(['destroyed_count' => $destroyedCount], "Successfully destroyed {$destroyedCount} organization bots.");
    }

    public function bulkRestore(BulkBotRequest $request, string $orgUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $restoredCount = $organization->bots()->whereIn('uuid', $request->input('ids'))->onlyTrashed()->restore();
        return $this->successResponse(['restored_count' => $restoredCount], "Successfully restored {$restoredCount} organization bots.");
    }

    public function bulkToggleStatus(BulkBotRequest $request, string $orgUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $updatedCount = $organization->bots()->whereIn('uuid', $request->input('ids'))->update(['status' => $request->input('status')]);
        return $this->successResponse(['updated_count' => $updatedCount], "Successfully updated {$updatedCount} organization bots to {$request->input('status')}.");
    }

    public function bulkDuplicate(BulkBotRequest $request, string $orgUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $duplicatedBots = DB::transaction(function () use ($request, $organization) {
            $originalBots = $organization->bots()->with('knowledgeBases')->whereIn('uuid', $request->input('ids'))->get();
            $newBots = [];

            foreach ($originalBots as $originalBot) {
                $duplicate = $originalBot->replicate();
                $duplicate->name = $originalBot->name . ' (Copy)';
                $duplicate->api_key = 'pk_' . Str::random(48);
                $duplicate->save();

                $knowledgeBaseIds = $originalBot->knowledgeBases->pluck('id');
                if ($knowledgeBaseIds->isNotEmpty()) {
                    $duplicate->knowledgeBases()->sync($knowledgeBaseIds);
                }
                $newBots[] = $duplicate->fresh(['aiModel:id,key,name', 'owner:id,name']);
            }
            return $newBots;
        });

        return $this->successResponse(
            ['duplicated_bots' => $duplicatedBots, 'count' => count($duplicatedBots)],
            "Successfully duplicated " . count($duplicatedBots) . " organization bots."
        );
    }
    public function handleLogoUpload(LogoUploadRequest $request, string $orgUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $file = $request->file('file');
        $path = $file->storeAs('logos/bots/temp/', time() . '_' . $file->getClientOriginalName(), 'public');

        return $this->successResponse(
            ['logo' => $path, 'logo_url' => url(Storage::url($path))],
            'Logo uploaded successfully.'
        );
    }

    private function findOrganization(string $uuid): Organization
    {
        return Organization::where('uuid', $uuid)->firstOrFail();
    }

    private function findBot(Organization $organization, string $uuid, bool $withTrashed = false): Bot
    {
        $query = $organization->bots();
        if ($withTrashed) {
            $query->withTrashed();
        }
        return $query->where('uuid', $uuid)->firstOrFail();
    }

    private function processKnowledgeSources(Request $request, Bot $bot, Organization $organization): void
    {
        if ($request->has('knowledge_sources')) {
            $knowledgeBaseIds = $organization->knowledgeBases()
                ->whereIn('uuid', $request->input('knowledge_sources', []))
                ->pluck('id');
            $bot->knowledgeBases()->sync($knowledgeBaseIds);
        }
    }

    private function prepareBotData(Request $request, int $organizationId): array
    {
        $data = $request->except(['knowledge_sources']);
        $knowledgeData = $request->input('knowledge', []);

        $data['owner_id'] = $organizationId;
        $data['owner_type'] = Organization::class;
        $data['model_ai_id'] = ModelAIFacade::getModelByKey($request->input('model'))?->id;
        $data['status'] = 'active';
        $data['metadata'] = [
            'rag_config' => [
                'enabled' => $knowledgeData['enabled'] ?? true,
                'auto_query' => $knowledgeData['auto_query'] ?? false,
                'top_k' => $knowledgeData['top_k'] ?? 5,
                'collection' => 'documents'
            ]
        ];
        return $data;
    }

    public function assignUsers(BulkBotRequest $request, string $orgUuid, string $botUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $bot = $this->findBot($organization, $botUuid);

        if ($bot->visibility !== BotVisibility::PRIVATE) {
            return $this->errorResponse(null, __('Can only assign users to private bots.'), 422);
        }

        $userIds = User::whereIn('uuid', $request->validated('ids'))->pluck('id');

        $assignedBy = auth()->id();

        $assignments = [];
        foreach ($userIds as $userId) {
            // Check if user is organization member
            if (!$organization->hasMember(User::find($userId))) {
                continue;
            }

            $assignments[] = [
                'bot_id' => $bot->id,
                'user_id' => $userId,
                'assigned_by' => $assignedBy,
                'assigned_at' => now(),
                'created_at' => now(),
                'updated_at' => now()
            ];
        }

        if (!empty($assignments)) {
            BotUser::insertOrIgnore($assignments);

            // Send notifications
            foreach ($userIds as $userId) {
                $user = User::find($userId);
                notify('organization.bot.access_granted', [
                    'user' => $user,
                    'organization' => $organization,
                    'bot' => $bot,
                    'granted_by' => auth()->user()
                ]);
            }
        }

        return $this->successResponse(
            ['assigned_count' => count($assignments)],
            __(':count users assigned to bot successfully.', ['count' => count($assignments)])
        );
    }

    public function unassignUsers(BulkBotRequest $request, string $orgUuid, string $botUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $bot = $this->findBot($organization, $botUuid);

        $userIds = User::whereIn('uuid', $request->validated('ids'))->pluck('id');

        $removedCount = $bot->botUsers()->whereIn('user_id', $userIds)->delete();

        // Send notifications
        foreach ($userIds as $userId) {
            $user = User::find($userId);
            notify('organization.bot.access_revoked', [
                'user' => $user,
                'organization' => $organization,
                'bot' => $bot,
                'revoked_by' => auth()->user()
            ]);
        }

        return $this->successResponse(
            ['unassigned_count' => $removedCount],
            __(':count users unassigned from bot successfully.', ['count' => $removedCount])
        );
    }

    public function getBotUsers(ViewBotRequest $request, string $orgUuid, string $botUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $bot = $this->findBot($organization, $botUuid);
        $user = $request->user();
        $userRole = $organization->getMemberRole($user);

        // Only super-admin, organization owner, admin, or bot owner can view assigned users
        if (!$user->hasRole('super-admin') &&
            !$organization->isOwner($user) &&
            $userRole !== 'admin' &&
            $bot->owner_id !== $user->id) {
            return $this->errorResponse(null, __('You do not have permission to view bot assigned users.'), 403);
        }

        $users = $bot->assignedUsers()
            ->select('users.uuid', 'users.first_name', 'users.last_name', 'users.email')
            ->paginate($request->input('limit', 10));

        return $this->paginatedResponse($users, __('Bot assigned users retrieved successfully.'));
    }

    /**
     * Get assigned users for a bot (alias for getBotUsers)
     */
    public function getAssignedUsers(ViewBotRequest $request, string $orgUuid, string $botUuid): JsonResponse
    {
        return $this->getBotUsers($request, $orgUuid, $botUuid);
    }

}