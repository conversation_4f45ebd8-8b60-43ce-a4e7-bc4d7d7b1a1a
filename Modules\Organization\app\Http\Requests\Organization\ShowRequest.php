<?php

namespace Modules\Organization\Http\Requests\Organization;

use Modules\Core\Http\Requests\BaseFormRequest;
use Modules\Organization\Models\Organization;

class ShowRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            // No additional validation rules needed
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $id = $this->id;
        $user = $this->user();

        if (!$id || !$user) {
            return false;
        }

        // Find the organization
        $organization = Organization::where('uuid', $id)->first();

        if (!$organization) {
            return false;
        }

        // Use Policy to check if user can view this organization
        return $user->can('view', $organization);
    }
}
