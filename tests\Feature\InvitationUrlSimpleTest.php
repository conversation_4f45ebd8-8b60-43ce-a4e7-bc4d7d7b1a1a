<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Modules\User\Models\User;
use Modules\Organization\Models\Organization;
use Modules\Organization\Models\OrganizationInvitation;
use Modules\Organization\Models\OrganizationMember;
use Illuminate\Support\Str;

class InvitationUrlSimpleTest extends TestCase
{
    use RefreshDatabase;

    protected $organization;
    protected $inviter;
    protected $invitee;
    protected $invitation;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test users
        $this->inviter = User::factory()->create([
            'email' => '<EMAIL>',
            'first_name' => '<PERSON>',
            'last_name' => 'Doe'
        ]);

        $this->invitee = User::factory()->create([
            'email' => '<EMAIL>',
            'first_name' => 'Jane',
            'last_name' => 'Smith'
        ]);

        // Create organization
        $this->organization = Organization::factory()->create([
            'name' => 'Test Organization'
        ]);

        // Create organization member for inviter
        OrganizationMember::create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->inviter->id,
            'role' => 'admin'
        ]);

        // Create invitation
        $this->invitation = OrganizationInvitation::create([
            'organization_id' => $this->organization->id,
            'inviter_id' => $this->inviter->id,
            'email' => $this->invitee->email,
            'role' => 'member',
            'token' => Str::random(64),
            'expires_at' => now()->addDays(7),
            'status' => 'pending',
            'message' => 'Welcome!'
        ]);
    }

    public function test_accept_invitation_creates_member()
    {
        $response = $this->get("/invitations/accept?token={$this->invitation->token}");

        $response->assertStatus(302);
        $response->assertRedirect('/auth');
        $response->assertSessionHas('success');

        // Check if member was created
        $this->assertDatabaseHas('organization_members', [
            'organization_id' => $this->organization->id,
            'user_id' => $this->invitee->id,
            'role' => 'member'
        ]);

        // Check if invitation status was updated
        $this->assertDatabaseHas('organization_invitations', [
            'id' => $this->invitation->id,
            'status' => 'accepted'
        ]);
    }

    public function test_decline_invitation_updates_status()
    {
        $response = $this->get("/invitations/decline?token={$this->invitation->token}");

        $response->assertStatus(302);
        $response->assertRedirect('/auth');
        $response->assertSessionHas('success');

        // Check if invitation status was updated
        $this->assertDatabaseHas('organization_invitations', [
            'id' => $this->invitation->id,
            'status' => 'declined'
        ]);

        // Check that no member was created
        $this->assertDatabaseMissing('organization_members', [
            'organization_id' => $this->organization->id,
            'user_id' => $this->invitee->id
        ]);
    }

    public function test_invalid_token_redirects_with_error()
    {
        $invalidToken = Str::random(64);
        $response = $this->get("/invitations/accept?token={$invalidToken}");

        $response->assertStatus(302);
        $response->assertRedirect('/auth');
        $response->assertSessionHas('error');
    }

    public function test_expired_invitation_redirects_with_error()
    {
        // Update invitation to be expired
        $this->invitation->update(['expires_at' => now()->subDays(1)]);

        $response = $this->get("/invitations/accept?token={$this->invitation->token}");

        $response->assertStatus(302);
        $response->assertRedirect('/auth');
        $response->assertSessionHas('error');
    }

    public function test_duplicate_membership_prevented()
    {
        // Create existing membership
        OrganizationMember::create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->invitee->id,
            'role' => 'viewer'
        ]);

        $response = $this->get("/invitations/accept?token={$this->invitation->token}");

        $response->assertStatus(302);
        $response->assertRedirect('/auth');
        $response->assertSessionHas('error');
    }
}
