<?php

namespace Modules\Organization\Http\Requests\Member;

use Modules\Core\Http\Requests\BaseFormRequest;
use Modules\Organization\Traits\OrganizationAuthorization;

class DeleteMemberRequest extends BaseFormRequest
{
    use OrganizationAuthorization;
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->hasOrganizationAdminAccess();
    }
}
