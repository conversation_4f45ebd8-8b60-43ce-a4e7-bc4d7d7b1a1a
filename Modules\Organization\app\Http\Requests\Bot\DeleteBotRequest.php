<?php

namespace Modules\Organization\Http\Requests\Bot;

use Modules\Core\Http\Requests\BaseFormRequest;
use Modules\Organization\Traits\OrganizationAuthorization;

class DeleteBotRequest extends BaseFormRequest
{
    use OrganizationAuthorization;

    public function rules(): array
    {
        return [];
    }

    public function authorize(): bool
    {
        return $this->hasOrganizationAdminAccess();
    }
}
