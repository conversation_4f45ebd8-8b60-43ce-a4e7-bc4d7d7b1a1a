<?php

namespace Modules\Organization\Http\Controllers\Auth;

use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Modules\Core\Traits\ResponseTrait;
use Modules\Organization\Http\Requests\Guest\ViewGuestRequest;
use Modules\Organization\Http\Requests\Guest\GuestRequest;
use Modules\Organization\Http\Requests\Guest\DeleteGuestRequest;
use Modules\Organization\Http\Requests\Guest\BulkGuestRequest;
use Modules\Organization\Models\Organization;
use Modules\Organization\Models\OrganizationInvitation;
use Modules\Organization\Http\Filters\GuestFilter;
use Modules\Organization\Traits\OrganizationAuthorization;
use Modules\User\Models\User;
use Modules\Organization\Http\Resources\InvitationNotificationResource;
use Modules\Organization\Http\Resources\OrganizationGuestResource;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

class GuestController extends Controller
{
    use ResponseTrait, OrganizationAuthorization;

    public function __construct()
    {
    }

    public function index(ViewGuestRequest $request, string $orgUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $guests = $organization->guests()
            ->filter(new GuestFilter($request))
            ->paginate($request->input('limit', 10));

        // Hide sensitive pivot fields
        $guests->getCollection()->map(function ($guest) {
            $guest->pivot->makeHidden(['user_id', 'organization_id']);
            return $guest;
        });

        return $this->paginatedResponse($guests, __('Organization guests retrieved successfully.'));
    }

    public function find(Request $request, string $orgUuid)
    {
        $organization = $this->getOrganization();

        $user = User::query()
            ->whereDoesntHave('organizations', function ($query) use ($organization) {
                $query->where('organization_id', $organization->id);
            })
            ->active()
            ->where(function (Builder $query) use ($request) {
                $query->where('username', $request->input('search'))
                    ->orWhere('full_name', $request->input('search'))
                    ->orWhere('phone', $request->input('search'))
                    ->orWhere('email', $request->input('search'));
            })
            ->get();

        return $this->successResponse($user, __('Organization guest retrieved successfully.'));
    }

    public function store(GuestRequest $request, string $orgUuid): JsonResponse
    {
        $organization = $this->getOrganization();

        $invitesData = collect($request->input('invites', []))->keyBy('uuid');
        $userUuids = $invitesData->keys();

        if ($userUuids->isEmpty()) {
            return $this->errorResponse(__('No users selected for invitation.'), 422);
        }


        $usersToInvite = User::whereIn('uuid', $userUuids)
            ->whereDoesntHave('organizations', function ($query) use ($organization) {
                $query->where('organization_id', $organization->id);
            })
            ->get();

        if ($usersToInvite->isEmpty()) {
            return $this->errorResponse(
                __('All selected users are already guests or do not exist.'),
                422
            );
        }

        DB::transaction(function () use ($usersToInvite, $organization, $invitesData, $request) {
            foreach ($usersToInvite as $user) {
                $role = $invitesData[$user->uuid]['role'] ?? 'guest';

                $invitation = OrganizationInvitation::create([
                    'organization_id' => $organization->id,
                    'inviter_id'      => auth()->id(),
                    'email'           => $user->email,
                    'role'            => $role,
                    'message'         => $request->input('message'),
                    'token'           => Str::random(64),
                    'expires_at'      => now()->addDays(7),
                    'status'          => 'pending',
                ]);

                $user->makeVisible(['id']);

                notify('organization.guest.invited',
                    InvitationNotificationResource::makeArray([
                        'user' => $user,
                        'organization' => $organization,
                        'inviter' => $invitation->inviter,
                        'invitation' => $invitation
                    ])
                );
            }
        });

        return $this->successResponse(
            ['invited_count' => $usersToInvite->count()],
            __('Invitations sent successfully.')
        );
    }

    // Guest addition now handled through invitation system only

    public function show(ViewGuestRequest $request, string $orgUuid, string $userUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $guest = $this->findGuest($organization, $userUuid);
        return $this->successResponse($guest, __('Organization guest retrieved successfully.'));
    }

    public function update(GuestRequest $request, string $orgUuid, string $userUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $guest = $this->findGuest($organization, $userUuid);

        $oldRole = $organization->getGuestRole($guest);
        $newRole = $request->validated('role');

        $organization->guests()->updateExistingPivot($guest->id, ['role' => $newRole]);

        // Send notification
        notify('organization.guest.role_changed', [
            'user' => $guest,
            'organization' => $organization,
            'old_role' => $oldRole,
            'new_role' => $newRole,
            'changed_by' => auth()->user()
        ]);

        return $this->successResponse(
            $this->findGuest($organization, $userUuid),
            __('Guest updated successfully.')
        );
    }

    public function destroy(DeleteGuestRequest $request, string $orgUuid, string $userUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $guest = $this->findGuest($organization, $userUuid);


        if ($organization->owner_id === $guest->id) {
            return $this->errorResponse(null, __('Cannot remove organization owner.'), 422);
        }

        $organization->guests()->detach($guest->id);

        return $this->successResponse(null, __('Guest removed from organization successfully.'));
    }

    public function bulkDestroy(DeleteGuestRequest $request, string $orgUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $userIds = User::whereIn('uuid', $request->validated('ids'))->pluck('id');
        $deletedCount = $organization->guests()->detach($userIds);

        return $this->successResponse(['deleted_count' => $deletedCount], __('Guests removed successfully.'));
    }

    public function dropdown(ViewGuestRequest $request, string $orgUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $guests = $organization->guests()
            ->whereIn('organization_guests.role', ['admin', 'guest', 'editor', 'viewer'])
            ->get(['users.uuid', 'users.first_name', 'users.last_name', 'users.email', 'organization_guests.role'])
            ->map(fn($guest) => [
                'id' => $guest->uuid,
                'name' => $guest->first_name . ' ' . $guest->last_name,
                'email' => $guest->email,
                'role' => $guest->pivot->role,
            ]);

        return $this->successResponse($guests, __('Organization guests dropdown retrieved successfully.'));
    }

    /**
     * Get all guests including guests for the organization
     * Optionally exclude users already assigned to a specific bot
     */
    public function allGuests(ViewGuestRequest $request, string $orgUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $user = $request->user();
        $userRole = $organization->getGuestRole($user);

        // Only super-admin, organization owner, or admin can view all guests including guests
        if (!$user->hasRole('super-admin') &&
            !$organization->isOwner($user) &&
            $userRole !== 'admin') {
            return $this->errorResponse(null, __('You do not have permission to view all organization guests.'), 403);
        }

        $query = $organization->guests()
            ->whereIn('organization_guests.role', ['admin', 'guest', 'editor', 'viewer', 'guest']);

        // If bot_uuid is provided, exclude users already assigned to that bot
        if ($request->has('exclude_bot_users') && $request->filled('bot_uuid')) {
            $botUuid = $request->input('bot_uuid');
            $bot = $organization->bots()->where('uuid', $botUuid)->first();

            if ($bot) {
                $query->whereNotIn('users.id', function($subQuery) use ($bot) {
                    $subQuery->select('user_id')
                             ->from('bot_users')
                             ->where('bot_id', $bot->id);
                });
            }
        }

        $guests = $query->filter(new GuestFilter($request))
                        ->paginate($request->input('limit', 10));

        // Hide sensitive fields and add role information
        $guests->getCollection()->map(function ($guest) {
            // Hide sensitive user fields
            $guest->makeHidden([
                'id',
                'email_verified_at',
                'password',
                'reguest_token',
                'created_at',
                'updated_at',
                'deleted_at',
                'phone',
                'address'
            ]);

            // Hide sensitive pivot fields
            $guest->pivot->makeHidden(['user_id', 'organization_id', 'id']);

            // Add role information
            $guest->setAttribute('role', $guest->pivot->role);
            $guest->setAttribute('joined_at', $guest->pivot->created_at);

            return $guest;
        });

        return $this->paginatedResponse($guests, __('All organization guests retrieved successfully.'));
    }

    /**
     * Get guests by role (including guests)
     */
    public function guestsByRole(ViewGuestRequest $request, string $orgUuid, string $role): JsonResponse
    {
        $organization = $this->getOrganization();
        $user = $request->user();
        $userRole = $organization->getGuestRole($user);

        // Validate role parameter
        $allowedRoles = ['admin', 'guest', 'editor', 'viewer', 'guest'];
        if (!in_array($role, $allowedRoles)) {
            return $this->errorResponse(null, __('Invalid role specified.'), 422);
        }

        // Only super-admin, organization owner, or admin can view guests by role
        if (!$user->hasRole('super-admin') &&
            !$organization->isOwner($user) &&
            $userRole !== 'admin') {
            return $this->errorResponse(null, __('You do not have permission to view guests by role.'), 403);
        }

        $guests = $organization->guests()
            ->where('organization_guests.role', $role)
            ->filter(new GuestFilter($request))
            ->paginate($request->input('limit', 10));

        // Hide sensitive pivot fields and add role information
        $guests->getCollection()->map(function ($guest) use ($role) {
            $guest->pivot->makeHidden(['user_id', 'organization_id']);
            $guest->setAttribute('role', $role);
            $guest->setAttribute('joined_at', $guest->pivot->created_at);
            return $guest;
        });

        return $this->paginatedResponse($guests, __('Organization :role guests retrieved successfully.', ['role' => $role]));
    }

    public function bulkUpdateRole(BulkGuestRequest $request, string $orgUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $role = $request->validated('role');
        $userIds = User::whereIn('uuid', $request->validated('ids'))->pluck('id');

        $usersToUpdate = $userIds->filter(fn($id) => $id !== $organization->owner_id);

        $syncData = $usersToUpdate->mapWithKeys(fn($id) => [$id => ['role' => $role]])->all();
        $organization->guests()->syncWithoutDetaching($syncData);

        return $this->successResponse(['updated_count' => count($usersToUpdate)], __('Guests roles updated successfully.'));
    }

    private function findGuest(Organization $organization, string $userUuid): User
    {
        return $organization->guests()->where('users.uuid', $userUuid)->firstOrFail();
    }


}



