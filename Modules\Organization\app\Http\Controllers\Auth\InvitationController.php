<?php

namespace Modules\Organization\Http\Controllers\Auth;

use Illuminate\Http\JsonResponse;
use Illuminate\Support\Str;
use App\Http\Controllers\Controller;
use Modules\Core\Traits\ResponseTrait;
use Modules\Organization\Http\Requests\Invitation\ViewInvitationRequest;
use Modules\Organization\Http\Requests\Invitation\InvitationRequest;
use Modules\Organization\Http\Requests\Invitation\DeleteInvitationRequest;
use Modules\Organization\Http\Requests\Invitation\BulkInvitationRequest;
use Modules\Organization\Http\Filters\InvitationFilter;
use Modules\Organization\Models\OrganizationInvitation;
use Modules\Organization\Models\OrganizationMember;
use Modules\Organization\Traits\OrganizationAuthorization;
use Modules\Organization\Http\Resources\InvitationNotificationResource;
use Modules\User\Models\User;
use Illuminate\Http\Request;

class InvitationController extends Controller
{
    use ResponseTrait, OrganizationAuthorization;

    public function index(ViewInvitationRequest $request, string $orgUuid): JsonResponse
    {
        $organization = $this->getOrganization();

        $invitations = $organization->invitations()
            ->with([
                'inviter:id,first_name,last_name,full_name,email',
                'invitee:email,first_name,last_name,full_name'
            ])
            ->filter(new InvitationFilter($request))
            ->paginate($request->input('limit', 10));

        return $this->paginatedResponse($invitations, __('Organization invitations retrieved successfully.'));
    }

    public function store(InvitationRequest $request, string $orgUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $user = User::where('email', $request->input('email'))->first();
        
        // Check if user is already a member
        if ($organization->hasMember($user)) {
            return $this->errorResponse(null, __('User is already a member of this organization.'), 422);
        }
        
        // Check if invitation already exists
        $existingInvitation = $organization->invitations()
            ->where('email', $request->input('email'))
            ->where('status', 'pending')
            ->first();
            
        if ($existingInvitation) {
            return $this->errorResponse(null, __('Invitation already sent to this email.'), 422);
        }

        $invitation = OrganizationInvitation::create([
            'organization_id' => $organization->id,
            'inviter_id' => auth()->id(),
            'email' => $request->input('email'),
            'role' => $request->input('role'),
            'message' => $request->input('message'),
            'token' => Str::random(64),
            'expires_at' => now()->addDays(7),
            'status' => 'pending'
        ]);

        // Send notification if enabled in organization settings
        $notificationKey = $request->input('role') === 'guest'
                ? 'organization.guest.invited'
                : 'organization.member.invited';

            notify($notificationKey,
                InvitationNotificationResource::makeArray([
                    'user' => $user,
                    'organization' => $organization,
                    'inviter' => auth()->user(),
                    'invitation' => $invitation
                ])
            );

        return $this->successResponse(
            $invitation->load(['inviter:id,first_name,last_name,email']),
            __('Invitation sent successfully.'),
            201
        );
    }

    public function show(ViewInvitationRequest $request, string $orgUuid, int $invitationId): JsonResponse
    {
        $organization = $this->getOrganization();
        $invitation = $organization->invitations()
            ->with(['inviter:id,first_name,last_name,email'])
            ->findOrFail($invitationId);

        return $this->successResponse($invitation, __('Invitation retrieved successfully.'));
    }

    public function resend(DeleteInvitationRequest $request, string $orgUuid, int $invitationId): JsonResponse
    {
        $organization = $this->getOrganization();
        $invitation = $organization->invitations()->findOrFail($invitationId);
        
        if ($invitation->status !== 'pending') {
            return $this->errorResponse(null, __('Can only resend pending invitations.'), 422);
        }

        // Update token and expiry
        $invitation->update([
            'token' => Str::random(64),
            'expires_at' => now()->addDays(7)
        ]);

        // Resend notification
        $user = User::where('email', $invitation->email)->first();
        $notificationKey = $invitation->role === 'guest' 
            ? 'organization.guest.invited' 
            : 'organization.member.invited';
            
        notify($notificationKey,
            InvitationNotificationResource::makeArray([
                'user' => $user,
                'organization' => $organization,
                'inviter' => $invitation->inviter,
                'invitation' => $invitation
            ])
        );

        return $this->successResponse($invitation->fresh(), __('Invitation resent successfully.'));
    }

    public function cancel(DeleteInvitationRequest $request, string $orgUuid, int $invitationId): JsonResponse
    {
        $organization = $this->getOrganization();
        $invitation = $organization->invitations()->findOrFail($invitationId);
        
        if ($invitation->status !== 'pending') {
            return $this->errorResponse(null, __('Can only cancel pending invitations.'), 422);
        }

        $invitation->update(['status' => 'cancelled']);

        return $this->successResponse(null, __('Invitation cancelled successfully.'));
    }

    /**
     * Handle invitation acceptance via GET request.
     */
    public function acceptInvitation(Request $request)
    {
        $token = $request->query('token');

        if (!$token) {
            return redirect('/auth')->with('error', __('Invalid invitation link.'));
        }

        $invitation = OrganizationInvitation::where('token', $token)
            ->where('status', 'pending')
            ->where('expires_at', '>', now())
            ->with(['organization', 'inviter'])
            ->first();

        if (!$invitation) {
            return redirect('/auth')->with('error', __('Invalid or expired invitation.'));
        }

        $user = User::where('email', $invitation->email)->first();
        if (!$user) {
            return redirect('/auth')->with('error', __('User not found.'));
        }

        // Check if user is already a member
        $existingMember = OrganizationMember::where('organization_id', $invitation->organization_id)
            ->where('user_id', $user->id)
            ->first();

        if ($existingMember) {
            return redirect('/auth')->with('error', __('You are already a member of this organization.'));
        }

        // Create organization member
        OrganizationMember::create([
            'organization_id' => $invitation->organization_id,
            'user_id' => $user->id,
            'role' => $invitation->role
        ]);

        $invitation->update(['status' => 'accepted']);

        // Notify inviter
        notify('organization.invitation.accepted', [
            'user_name' => $user->first_name . ' ' . $user->last_name,
            'organization_name' => $invitation->organization->name,
            'role' => $invitation->role,
            'user' => $user,
            'organization' => $invitation->organization,
            'inviter' => $invitation->inviter
        ]);

        $message = __('Invitation accepted successfully. You are now a member of :org.', [
            'org' => $invitation->organization->name
        ]);

        return redirect('/auth')->with('success', $message);
    }

    /**
     * Handle invitation decline via GET request.
     */
    public function declineInvitation(Request $request)
    {
        $token = $request->query('token');

        if (!$token) {
            return redirect('/auth')->with('error', __('Invalid invitation link.'));
        }

        $invitation = OrganizationInvitation::where('token', $token)
            ->where('status', 'pending')
            ->where('expires_at', '>', now())
            ->with(['organization', 'inviter'])
            ->first();

        if (!$invitation) {
            return redirect('/auth')->with('error', __('Invalid or expired invitation.'));
        }

        $user = User::where('email', $invitation->email)->first();
        if (!$user) {
            return redirect('/auth')->with('error', __('User not found.'));
        }

        $invitation->update(['status' => 'declined']);

        // Notify inviter
        notify('organization.invitation.declined', [
            'user_name' => $user->first_name . ' ' . $user->last_name,
            'organization_name' => $invitation->organization->name,
            'role' => $invitation->role,
            'user' => $user,
            'organization' => $invitation->organization,
            'inviter' => $invitation->inviter
        ]);

        return redirect('/auth')->with('success', __('Invitation declined.'));
    }



    public function bulkCancel(BulkInvitationRequest $request, string $orgUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $ids = $request->validated('ids');
        
        $cancelledCount = $organization->invitations()
            ->whereIn('id', $ids)
            ->where('status', 'pending')
            ->update(['status' => 'cancelled']);

        return $this->successResponse(
            ['cancelled_count' => $cancelledCount],
            __(':count invitations cancelled successfully.', ['count' => $cancelledCount])
        );
    }

    public function bulkResend(BulkInvitationRequest $request, string $orgUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $ids = $request->validated('ids');
        
        $invitations = $organization->invitations()
            ->whereIn('id', $ids)
            ->where('status', 'pending')
            ->get();

        foreach ($invitations as $invitation) {
            // Update token and expiry
            $invitation->update([
                'token' => Str::random(64),
                'expires_at' => now()->addDays(7)
            ]);

            // Resend notification
            $user = User::where('email', $invitation->email)->first();
            $notificationKey = $invitation->role === 'guest' 
                ? 'organization.guest.invited' 
                : 'organization.member.invited';
                
            notify($notificationKey,
                InvitationNotificationResource::makeArray([
                    'user' => $user,
                    'organization' => $organization,
                    'inviter' => $invitation->inviter,
                    'invitation' => $invitation
                ])
            );
        }

        return $this->successResponse(
            ['resent_count' => $invitations->count()],
            __(':count invitations resent successfully.', ['count' => $invitations->count()])
        );
    }
}
