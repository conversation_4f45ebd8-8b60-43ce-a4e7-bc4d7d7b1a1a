<?php

namespace Modules\Organization\Http\Requests\KnowledgeBase;

use Modules\Core\Http\Requests\BaseFormRequest;
use Modules\Organization\Traits\OrganizationAuthorization;

class DeleteKnowledgeBaseRequest extends BaseFormRequest
{
    use OrganizationAuthorization;

    public function rules(): array
    {
        return [];
    }

    public function authorize(): bool
    {
        return $this->hasOrganizationAdminAccess();
    }
}
