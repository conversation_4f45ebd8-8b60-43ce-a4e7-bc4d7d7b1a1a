<?php

namespace Modules\Organization\Http\Requests\Invitation;

use Modules\Core\Http\Requests\BaseFormRequest;
use Modules\Organization\Traits\OrganizationAuthorization;

class ViewInvitationRequest extends BaseFormRequest
{
    use OrganizationAuthorization;

    public function rules(): array
    {
        return [
            'limit' => 'nullable|integer|min:1|max:100',
            'page' => 'nullable|integer|min:1',
            'search' => 'nullable|string|max:255',
            'role' => 'nullable|string|in:admin,editor,viewer,member,guest',
            'status' => 'nullable|string|in:pending,accepted,declined,expired',
            'inviter_name' => 'nullable|string|max:255',
            'sort_by' => 'nullable|string|in:email,role,created_at,expires_at',
            'sort_direction' => 'nullable|string|in:asc,desc',
        ];
    }

    public function authorize(): bool
    {
        return $this->hasOrganizationAccess();
    }
}
