<?php

namespace Modules\Organization\Http\Filters;

use Modules\Core\Abstracts\AbstractFilter;
use Carbon\Carbon;

class InvitationFilter extends AbstractFilter
{
    /**
     * Define the filters available for organization invitations.
     */
    protected function filters(): array
    {
        return [
            'email' => 'like',
            'role' => 'exact',
            'search' => 'custom',
            'status' => 'custom',
            'inviter_name' => 'custom'
        ];
    }

    /**
     * Apply custom filters.
     */
    protected function applyCustomFilter($query, string $field, mixed $value): void
    {
        if ($field === 'search') {
            $query->where('email', 'like', "%{$value}%");
        } elseif ($field === 'status') {
            if ($value === 'pending') {
                $query->where('expires_at', '>', Carbon::now());
            } elseif ($value === 'expired') {
                $query->where('expires_at', '<=', Carbon::now());
            }
        } elseif ($field === 'inviter_name') {
            $query->whereHas('inviter', function ($q) use ($value) {
                $q->where('first_name', 'like', "%{$value}%")
                  ->orWhere('last_name', 'like', "%{$value}%");
            });
        }
    }
}
