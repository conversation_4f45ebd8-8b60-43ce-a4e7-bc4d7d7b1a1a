<?php

namespace Modules\Organization\Http\Filters;

use Modules\Core\Abstracts\AbstractFilter;

class GuestFilter extends AbstractFilter
{
    /**
     * Define the filters available for organization guests.
     */
    protected function filters(): array
    {
        return [
            // Basic filters
            'role' => 'exact',
            'organization_id' => 'exact',
            'user_id' => 'exact'
        ];
    }

    /**
     * Define searchable fields for the search filter.
     */
    protected function searchableFields(): array
    {
        return [
            'role',
        ];
    }

    /**
     * Apply custom filters.
     */
    protected function applyCustomFilters(): void
    {
        // Filter by user name
        if ($this->request->filled('user_name')) {
            $this->query->whereHas('user', function ($query) {
                $userName = $this->request->input('user_name');
                $query->where('first_name', 'like', "%{$userName}%")
                      ->orWhere('last_name', 'like', "%{$userName}%")
                      ->orWhere('username', 'like', "%{$userName}%");
            });
        }

        // Filter by user email
        if ($this->request->filled('user_email')) {
            $this->query->whereHas('user', function ($query) {
                $userEmail = $this->request->input('user_email');
                $query->where('email', 'like', "%{$userEmail}%");
            });
        }

        // Filter by organization name
        if ($this->request->filled('organization_name')) {
            $this->query->whereHas('organization', function ($query) {
                $organizationName = $this->request->input('organization_name');
                $query->where('name', 'like', "%{$organizationName}%");
            });
        }

        // Filter guests only (ensure we only get guest role)
        $this->query->where('role', 'guest');

        // Filter by active users only
        if ($this->request->has('active_users_only')) {
            $activeUsersOnly = $this->request->boolean('active_users_only');
            if ($activeUsersOnly) {
                $this->query->whereHas('user', function ($query) {
                    $query->where('status', 'active');
                });
            }
        }

        // Filter by user status
        if ($this->request->filled('user_status')) {
            $this->query->whereHas('user', function ($query) {
                $userStatus = $this->request->input('user_status');
                $query->where('status', $userStatus);
            });
        }

        // Filter by membership duration (days)
        if ($this->request->filled('guest_since_days')) {
            $days = (int) $this->request->input('guest_since_days');
            $this->query->where('created_at', '<=', now()->subDays($days));
        }

        // Filter by recent guests (joined within X days)
        if ($this->request->filled('recent_guests_days')) {
            $days = (int) $this->request->input('recent_guests_days');
            $this->query->where('created_at', '>=', now()->subDays($days));
        }
    }
}
