<?php

namespace Modules\Organization\Http\Requests\Guest;

use Modules\Core\Http\Requests\BaseFormRequest;
use Modules\Organization\Traits\OrganizationAuthorization;

class BulkGuestRequest extends BaseFormRequest
{
    use OrganizationAuthorization;

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'ids' => [
                'required',
                'array',
                'min:1',
            ],
            'ids.*' => [
                'required',
                'string',
                'exists:organization_members,id',
            ],
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->hasOrganizationAdminAccess();
    }
}
