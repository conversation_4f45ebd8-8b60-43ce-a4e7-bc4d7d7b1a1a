<?php

namespace Modules\Organization\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class OrganizationNotificationTemplatesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Seeding remaining Organization notification templates...');
        $this->seedRemainingTemplates();
        $this->command->info('Organization notification templates seeding completed!');
    }

    /**
     * Seed remaining Organization notification templates.
     */
    private function seedRemainingTemplates(): void
    {
        // Get notification types
        $notificationTypes = DB::table('notification_types')
            ->whereIn('key', [
                'organization.guest.invited', 'organization.invitation.accepted',
                'organization.invitation.declined', 'organization.member.role_changed',
                'organization.member.removed', 'organization.bot.access_granted',
                'organization.bot.access_revoked'
            ])
            ->get()
            ->keyBy('key');

        $templates = [];

        // Guest Invitation Templates
        if (isset($notificationTypes['organization.guest.invited'])) {
            $guestInvitedType = $notificationTypes['organization.guest.invited'];

            $templates[] = [
                'notification_type_id' => $guestInvitedType->id,
                'channel' => 'database',
                'locale' => 'en',
                'subject' => null,
                'title' => 'Guest invitation to {organization_name}',
                'content' => 'You have been invited by {inviter_name} to join {organization_name} as guest.',
                'variables' => json_encode(['user_name', 'organization_name', 'inviter_name', 'accept_url', 'decline_url']),
                'settings' => json_encode([]),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];

            $templates[] = [
                'notification_type_id' => $guestInvitedType->id,
                'channel' => 'email',
                'locale' => 'en',
                'subject' => 'Guest invitation to {organization_name} - {app_name}',
                'title' => 'Guest Invitation',
                'content' => '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Guest Invitation - {app_name}</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">👥 Guest Invitation</h1>
    </div>
    
    <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;">
        <p style="font-size: 16px; margin-bottom: 20px;">Hello <strong>{user_name}</strong>,</p>
        
        <p style="font-size: 16px; margin-bottom: 20px;">
            <strong>{inviter_name}</strong> has invited you to join <strong>{organization_name}</strong> as a guest member.
        </p>
        
        <p style="font-size: 16px; margin-bottom: 20px;">
            As a guest, you will have limited access to organization resources and features.
        </p>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="{accept_url}" style="background: #17a2b8; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block; margin-right: 10px;">
                Accept Invitation
            </a>
            <a href="{decline_url}" style="background: #6c757d; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
                Decline
            </a>
        </div>
        
        <p style="font-size: 14px; color: #666; margin-top: 20px;">
            This invitation will expire in 7 days.
        </p>
        
        <hr style="border: none; border-top: 1px solid #e9ecef; margin: 30px 0;">
        
        <p style="font-size: 14px; color: #666; text-align: center; margin: 0;">
            Best regards,<br>
            The {app_name} Team
        </p>
    </div>
</body>
</html>',
                'variables' => json_encode(['user_name', 'organization_name', 'inviter_name', 'accept_url', 'decline_url', 'app_name']),
                'settings' => json_encode(['from_name' => '{app_name} Team']),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        // Invitation Accepted Templates
        if (isset($notificationTypes['organization.invitation.accepted'])) {
            $invitationAcceptedType = $notificationTypes['organization.invitation.accepted'];

            $templates[] = [
                'notification_type_id' => $invitationAcceptedType->id,
                'channel' => 'database',
                'locale' => 'en',
                'subject' => null,
                'title' => '{user_name} accepted your invitation',
                'content' => '{user_name} has accepted your invitation to join {organization_name}.',
                'variables' => json_encode(['user_name', 'organization_name', 'role']),
                'settings' => json_encode([]),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];

            $templates[] = [
                'notification_type_id' => $invitationAcceptedType->id,
                'channel' => 'email',
                'locale' => 'en',
                'subject' => 'Invitation accepted - {organization_name}',
                'title' => 'Invitation Accepted',
                'content' => '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invitation Accepted - {app_name}</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">✅ Invitation Accepted</h1>
    </div>
    
    <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;">
        <p style="font-size: 16px; margin-bottom: 20px;">Great news!</p>
        
        <p style="font-size: 16px; margin-bottom: 20px;">
            <strong>{user_name}</strong> has accepted your invitation to join <strong>{organization_name}</strong> as <strong>{role}</strong>.
        </p>
        
        <p style="font-size: 16px; margin-bottom: 20px;">
            They are now a member of your organization and can access the relevant features and resources.
        </p>
        
        <hr style="border: none; border-top: 1px solid #e9ecef; margin: 30px 0;">
        
        <p style="font-size: 14px; color: #666; text-align: center; margin: 0;">
            Best regards,<br>
            The {app_name} Team
        </p>
    </div>
</body>
</html>',
                'variables' => json_encode(['user_name', 'organization_name', 'role', 'app_name']),
                'settings' => json_encode(['from_name' => '{app_name} Team']),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        // Member Role Changed Templates
        if (isset($notificationTypes['organization.member.role_changed'])) {
            $roleChangedType = $notificationTypes['organization.member.role_changed'];

            $templates[] = [
                'notification_type_id' => $roleChangedType->id,
                'channel' => 'database',
                'locale' => 'en',
                'subject' => null,
                'title' => 'Your role in {organization_name} has been updated',
                'content' => 'Your role has been changed from {old_role} to {new_role} in {organization_name}.',
                'variables' => json_encode(['user_name', 'organization_name', 'old_role', 'new_role', 'changed_by']),
                'settings' => json_encode([]),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];

            $templates[] = [
                'notification_type_id' => $roleChangedType->id,
                'channel' => 'email',
                'locale' => 'en',
                'subject' => 'Role updated in {organization_name} - {app_name}',
                'title' => 'Role Updated',
                'content' => '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Role Updated - {app_name}</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">🔄 Role Updated</h1>
    </div>
    
    <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;">
        <p style="font-size: 16px; margin-bottom: 20px;">Hello <strong>{user_name}</strong>,</p>
        
        <p style="font-size: 16px; margin-bottom: 20px;">
            Your role in <strong>{organization_name}</strong> has been updated by <strong>{changed_by}</strong>.
        </p>
        
        <div style="background: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <p style="margin: 0; font-size: 16px;">
                <strong>Previous role:</strong> {old_role}<br>
                <strong>New role:</strong> {new_role}
            </p>
        </div>
        
        <p style="font-size: 16px; margin-bottom: 20px;">
            Your access permissions may have changed. Please log in to see your updated capabilities.
        </p>
        
        <hr style="border: none; border-top: 1px solid #e9ecef; margin: 30px 0;">
        
        <p style="font-size: 14px; color: #666; text-align: center; margin: 0;">
            Best regards,<br>
            The {app_name} Team
        </p>
    </div>
</body>
</html>',
                'variables' => json_encode(['user_name', 'organization_name', 'old_role', 'new_role', 'changed_by', 'app_name']),
                'settings' => json_encode(['from_name' => '{app_name} Team']),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        // Bot Access Granted Templates
        if (isset($notificationTypes['organization.bot.access_granted'])) {
            $botAccessGrantedType = $notificationTypes['organization.bot.access_granted'];

            $templates[] = [
                'notification_type_id' => $botAccessGrantedType->id,
                'channel' => 'database',
                'locale' => 'en',
                'subject' => null,
                'title' => 'Access granted to {bot_name}',
                'content' => 'You have been granted access to the private bot "{bot_name}" in {organization_name}.',
                'variables' => json_encode(['user_name', 'organization_name', 'bot_name', 'granted_by']),
                'settings' => json_encode([]),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        // Invitation Declined Templates
        if (isset($notificationTypes['organization.invitation.declined'])) {
            $invitationDeclinedType = $notificationTypes['organization.invitation.declined'];

            $templates[] = [
                'notification_type_id' => $invitationDeclinedType->id,
                'channel' => 'database',
                'locale' => 'en',
                'subject' => null,
                'title' => '{user_name} declined your invitation',
                'content' => '{user_name} has declined your invitation to join {organization_name}.',
                'variables' => json_encode(['user_name', 'organization_name', 'role']),
                'settings' => json_encode([]),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];

            $templates[] = [
                'notification_type_id' => $invitationDeclinedType->id,
                'channel' => 'email',
                'locale' => 'en',
                'subject' => 'Invitation declined - {organization_name}',
                'title' => 'Invitation Declined',
                'content' => '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invitation Declined - {app_name}</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">❌ Invitation Declined</h1>
    </div>

    <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;">
        <p style="font-size: 16px; margin-bottom: 20px;">Hello,</p>

        <p style="font-size: 16px; margin-bottom: 20px;">
            <strong>{user_name}</strong> has declined your invitation to join <strong>{organization_name}</strong> as {role}.
        </p>

        <p style="font-size: 16px; margin-bottom: 20px;">
            You can send a new invitation if needed.
        </p>

        <hr style="border: none; border-top: 1px solid #e9ecef; margin: 30px 0;">

        <p style="font-size: 14px; color: #666; text-align: center; margin: 0;">
            Best regards,<br>
            The {app_name} Team
        </p>
    </div>
</body>
</html>',
                'variables' => json_encode(['user_name', 'organization_name', 'role', 'app_name']),
                'settings' => json_encode(['from_name' => '{app_name} Team']),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        // Insert all templates
        foreach ($templates as $template) {
            DB::table('notification_templates')->updateOrInsert(
                [
                    'notification_type_id' => $template['notification_type_id'],
                    'channel' => $template['channel'],
                    'locale' => $template['locale']
                ],
                $template
            );
        }

        $this->command->info('Remaining Organization notification templates seeded successfully.');
    }
}
