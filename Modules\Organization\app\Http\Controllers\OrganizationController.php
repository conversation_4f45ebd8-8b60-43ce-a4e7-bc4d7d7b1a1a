<?php

namespace Modules\Organization\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Core\Traits\ResponseTrait;
use Modules\Organization\Facades\OrganizationFacade;

class OrganizationController extends Controller
{
    
    use ResponseTrait;
    
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $organizations = OrganizationFacade::getPublicOrganizations();

        return $this->successResponse($organizations, __('Organizations retrieved successfully.'));
    }


    /**
     * Display the specified organization.
     */
    public function show(string $uuid): JsonResponse
    {
        $organization = OrganizationFacade::getByUuid($uuid);

        if (!$organization) {
            return $this->notFoundResponse(null, __('Organization not found.'));
        }

        return $this->successResponse($organization, __('Organization retrieved successfully.'));
    }

    /**
     * Get invitation details by token (public endpoint).
     */
    public function details(string $token): JsonResponse
    {
        $validation = OrganizationFacade::validateInvitation($token);

        if (!$validation['valid']) {
            return $this->errorResponse(
                null,
                $validation['message'],
                404
            );
        }

        return $this->successResponse(
            $validation['invitation'], 
            __('Invitation details retrieved successfully.')
        );
    }

    /**
     * Accept invitation (public endpoint).
     */
    public function accept(Request $request, string $token): JsonResponse
    {
        $user = $request->user();
        if (!$user) {
            return $this->errorResponse(
                null,
                __('You must be logged in to accept invitations.'),
                401
            );
        }

        $result = OrganizationFacade::acceptInvitation($token, $user);

        if (!$result['success']) {
            return $this->errorResponse(
                null,
                $result['message'],
                422
            );
        }

        return $this->successResponse(
            $result['member'],
            $result['message']
        );
    }

    /**
     * Accept invitation as guest (public endpoint).
     */
    public function acceptAsGuest(Request $request, string $token): JsonResponse
    {
        $user = $request->user();
        if (!$user) {
            return $this->errorResponse(
                null,
                __('You must be logged in to accept invitations.'),
                401
            );
        }

        $result = OrganizationFacade::acceptInvitationAsGuest($token, $user);

        if (!$result['success']) {
            return $this->errorResponse(
                null,
                $result['message'],
                422
            );
        }

        return $this->successResponse(
            $result['member'],
            $result['message']
        );
    }
}
