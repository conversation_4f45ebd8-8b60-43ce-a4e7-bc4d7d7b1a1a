<?php

namespace Modules\Organization\Http\Controllers\Auth;

use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Modules\Core\Traits\ResponseTrait;
use Modules\Organization\Http\Requests\Member\ViewMemberRequest;
use Modules\Organization\Http\Requests\Member\MemberRequest;
use Modules\Organization\Http\Requests\Member\DeleteMemberRequest;
use Modules\Organization\Http\Requests\Member\BulkMemberRequest;
use Modules\Organization\Models\Organization;
use Modules\Organization\Models\OrganizationInvitation;
use Modules\Organization\Http\Filters\MemberFilter;
use Modules\Organization\Traits\OrganizationAuthorization;
use Modules\User\Models\User;
use Modules\Organization\Http\Resources\InvitationNotificationResource;
use Modules\Organization\Http\Resources\OrganizationMemberResource;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

class MemberController extends Controller
{
    use ResponseTrait, OrganizationAuthorization;

    public function __construct()
    {
    }

    public function index(ViewMemberRequest $request, string $orgUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $members = $organization->members()
            ->filter(new MemberFilter($request))
            ->paginate($request->input('limit', 10));

        // Hide sensitive pivot fields
        $members->getCollection()->map(function ($member) {
            $member->pivot->makeHidden(['user_id', 'organization_id']);
            return $member;
        });

        return $this->paginatedResponse($members, __('Organization members retrieved successfully.'));
    }

    public function find(Request $request, string $orgUuid)
    {
        $organization = $this->getOrganization();

        $user = User::query()
            ->whereDoesntHave('organizations', function ($query) use ($organization) {
                $query->where('organization_id', $organization->id);
            })
            ->active()
            ->where(function (Builder $query) use ($request) {
                $query->where('username', $request->input('search'))
                    ->orWhere('full_name', $request->input('search'))
                    ->orWhere('phone', $request->input('search'))
                    ->orWhere('email', $request->input('search'));
            })
            ->get();

        return $this->successResponse($user, __('Organization member retrieved successfully.'));
    }

    public function store(MemberRequest $request, string $orgUuid): JsonResponse
    {
        $organization = $this->getOrganization();

        $invitesData = collect($request->input('invites', []))->keyBy('uuid');
        $userUuids = $invitesData->keys();

        if ($userUuids->isEmpty()) {
            return $this->errorResponse(__('No users selected for invitation.'), 422);
        }


        $usersToInvite = User::whereIn('uuid', $userUuids)
            ->whereDoesntHave('organizations', function ($query) use ($organization) {
                $query->where('organization_id', $organization->id);
            })
            ->get();

        if ($usersToInvite->isEmpty()) {
            return $this->errorResponse(
                __('All selected users are already members or do not exist.'),
                422
            );
        }

        DB::transaction(function () use ($usersToInvite, $organization, $invitesData, $request) {
            foreach ($usersToInvite as $user) {
                $role = $invitesData[$user->uuid]['role'] ?? 'member';

                $invitation = OrganizationInvitation::create([
                    'organization_id' => $organization->id,
                    'inviter_id'      => auth()->id(),
                    'email'           => $user->email,
                    'role'            => $role,
                    'message'         => $request->input('message'),
                    'token'           => Str::random(64),
                    'expires_at'      => now()->addDays(7),
                    'status'          => 'pending',
                ]);

                $user->makeVisible(['id']);

                notify('organization.member.invited',
                    InvitationNotificationResource::makeArray([
                        'user' => $user,
                        'organization' => $organization,
                        'inviter' => $invitation->inviter,
                        'invitation' => $invitation
                    ])
                );
            }
        });

        return $this->successResponse(
            ['invited_count' => $usersToInvite->count()],
            __('Invitations sent successfully.')
        );
    }

    // Member addition now handled through invitation system only

    public function show(ViewMemberRequest $request, string $orgUuid, string $userUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $member = $this->findMember($organization, $userUuid);
        return $this->successResponse($member, __('Organization member retrieved successfully.'));
    }

    public function update(MemberRequest $request, string $orgUuid, string $userUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $member = $this->findMember($organization, $userUuid);

        $oldRole = $organization->getMemberRole($member);
        $newRole = $request->validated('role');

        $organization->members()->updateExistingPivot($member->id, ['role' => $newRole]);

        // Send notification
        notify('organization.member.role_changed', [
            'user' => $member,
            'organization' => $organization,
            'old_role' => $oldRole,
            'new_role' => $newRole,
            'changed_by' => auth()->user()
        ]);

        return $this->successResponse(
            $this->findMember($organization, $userUuid),
            __('Member updated successfully.')
        );
    }

    public function destroy(DeleteMemberRequest $request, string $orgUuid, string $userUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $member = $this->findMember($organization, $userUuid);


        if ($organization->owner_id === $member->id) {
            return $this->errorResponse(null, __('Cannot remove organization owner.'), 422);
        }

        $organization->members()->detach($member->id);

        return $this->successResponse(null, __('Member removed from organization successfully.'));
    }

    public function bulkDestroy(DeleteMemberRequest $request, string $orgUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $userIds = User::whereIn('uuid', $request->validated('ids'))->pluck('id');
        $deletedCount = $organization->members()->detach($userIds);

        return $this->successResponse(['deleted_count' => $deletedCount], __('Members removed successfully.'));
    }

    public function dropdown(ViewMemberRequest $request, string $orgUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $members = $organization->members()
            ->whereIn('organization_members.role', ['admin', 'member', 'editor', 'viewer'])
            ->get(['users.uuid', 'users.first_name', 'users.last_name', 'users.email', 'organization_members.role'])
            ->map(fn($member) => [
                'id' => $member->uuid,
                'name' => $member->first_name . ' ' . $member->last_name,
                'email' => $member->email,
                'role' => $member->pivot->role,
            ]);

        return $this->successResponse($members, __('Organization members dropdown retrieved successfully.'));
    }

    /**
     * Get all members including guests for the organization
     * Optionally exclude users already assigned to a specific bot
     */
    public function allMembers(ViewMemberRequest $request, string $orgUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $user = $request->user();
        $userRole = $organization->getMemberRole($user);

        // Only super-admin, organization owner, or admin can view all members including guests
        if (!$user->hasRole('super-admin') &&
            !$organization->isOwner($user) &&
            $userRole !== 'admin') {
            return $this->errorResponse(null, __('You do not have permission to view all organization members.'), 403);
        }

        $query = $organization->members()
            ->whereIn('organization_members.role', ['admin', 'member', 'editor', 'viewer', 'guest']);

        // If bot_uuid is provided, exclude users already assigned to that bot
        if ($request->has('exclude_bot_users') && $request->filled('bot_uuid')) {
            $botUuid = $request->input('bot_uuid');
            $bot = $organization->bots()->where('uuid', $botUuid)->first();

            if ($bot) {
                $query->whereNotIn('users.id', function($subQuery) use ($bot) {
                    $subQuery->select('user_id')
                             ->from('bot_users')
                             ->where('bot_id', $bot->id);
                });
            }
        }

        $members = $query->filter(new MemberFilter($request))
                        ->paginate($request->input('limit', 10));

        // Hide sensitive fields and add role information
        $members->getCollection()->map(function ($member) {
            // Hide sensitive user fields
            $member->makeHidden([
                'id',
                'email_verified_at',
                'password',
                'remember_token',
                'created_at',
                'updated_at',
                'deleted_at',
                'phone',
                'address'
            ]);

            // Hide sensitive pivot fields
            $member->pivot->makeHidden(['user_id', 'organization_id', 'id']);

            // Add role information
            $member->setAttribute('role', $member->pivot->role);
            $member->setAttribute('joined_at', $member->pivot->created_at);

            return $member;
        });

        return $this->paginatedResponse($members, __('All organization members retrieved successfully.'));
    }

    /**
     * Get members by role (including guests)
     */
    public function membersByRole(ViewMemberRequest $request, string $orgUuid, string $role): JsonResponse
    {
        $organization = $this->getOrganization();
        $user = $request->user();
        $userRole = $organization->getMemberRole($user);

        // Validate role parameter
        $allowedRoles = ['admin', 'member', 'editor', 'viewer', 'guest'];
        if (!in_array($role, $allowedRoles)) {
            return $this->errorResponse(null, __('Invalid role specified.'), 422);
        }

        // Only super-admin, organization owner, or admin can view members by role
        if (!$user->hasRole('super-admin') &&
            !$organization->isOwner($user) &&
            $userRole !== 'admin') {
            return $this->errorResponse(null, __('You do not have permission to view members by role.'), 403);
        }

        $members = $organization->members()
            ->where('organization_members.role', $role)
            ->filter(new MemberFilter($request))
            ->paginate($request->input('limit', 10));

        // Hide sensitive pivot fields and add role information
        $members->getCollection()->map(function ($member) use ($role) {
            $member->pivot->makeHidden(['user_id', 'organization_id']);
            $member->setAttribute('role', $role);
            $member->setAttribute('joined_at', $member->pivot->created_at);
            return $member;
        });

        return $this->paginatedResponse($members, __('Organization :role members retrieved successfully.', ['role' => $role]));
    }

    public function bulkUpdateRole(BulkMemberRequest $request, string $orgUuid): JsonResponse
    {
        $organization = $this->getOrganization();
        $role = $request->validated('role');
        $userIds = User::whereIn('uuid', $request->validated('ids'))->pluck('id');

        $usersToUpdate = $userIds->filter(fn($id) => $id !== $organization->owner_id);

        $syncData = $usersToUpdate->mapWithKeys(fn($id) => [$id => ['role' => $role]])->all();
        $organization->members()->syncWithoutDetaching($syncData);

        return $this->successResponse(['updated_count' => count($usersToUpdate)], __('Members roles updated successfully.'));
    }

    private function findMember(Organization $organization, string $userUuid): User
    {
        return $organization->members()->where('users.uuid', $userUuid)->firstOrFail();
    }


}



